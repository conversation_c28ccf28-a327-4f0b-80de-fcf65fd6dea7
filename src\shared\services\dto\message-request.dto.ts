import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  ValidateNested,
  IsBoolean,
  IsArray,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TextContentBlockDto } from './text-content-block.dto';
import { ImageContentBlockDto } from './image-content-block.dto';
import { FileContentBlockDto } from './file-content-block.dto';
import { ToolCallDecisionDto } from './tool-call-decision.dto';
// ✅ REMOVED: ReplyToDto - replaced by flat replyToMessageId design
import {
  AttachmentContextArrayDto,
  FileAttachmentContextDto,
  ImageAttachmentContextDto,
} from './attachment-context.dto';

/**
 * ✅ UPDATED: DTO for content block types (ReplyToDto removed - use replyToMessageId field instead)
 */
type CreateContentDto =
  | TextContentBlockDto
  | ImageContentBlockDto
  | FileContentBlockDto
  | ToolCallDecisionDto;
export class MessageRequestDto {
  @ApiProperty({
    description: 'An array of content blocks that make up the message.',
    type: 'array',
    items: {
      oneOf: [
        { $ref: '#/components/schemas/TextContentBlockDto' },
        { $ref: '#/components/schemas/ImageContentBlockDto' },
        { $ref: '#/components/schemas/FileContentBlockDto' },
        { $ref: '#/components/schemas/ToolCallDecisionDto' },
      ],
    },
    examples: [
      [{ type: 'text', content: 'Hello, how can I help you?' }],
      [
        { type: 'text', content: 'Here are the files you requested:' },
        { type: 'image', imageId: 'deac627b-8be6-4d15-a050-f2074cdbbc55' },
        { type: 'file', fileId: 'deac627b-8be6-4d15-a050-f2074cdbbc55' }
      ]
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object, {
    keepDiscriminatorProperty: true,
    discriminator: {
      property: 'type', // The field in the JSON that holds the type name
      subTypes: [
        { value: TextContentBlockDto, name: 'text' },
        { value: ImageContentBlockDto, name: 'image' },
        { value: FileContentBlockDto, name: 'file' },
        { value: ToolCallDecisionDto, name: 'tool_call_decision' },
      ],
    },
  })
   @OnlyOneToolCallDecisionBlockAllowed({
    message:
      'Only one "tool_call_decision" block is allowed, and it must be the only block.',
  })
  contentBlocks: CreateContentDto[];

  @ApiProperty({
    description: 'Required thread ID to continue existing conversation',
    example: 'deac627b-8be6-4d15-a050-f2074cdbbc55',
  })
  @IsNotEmpty()
  @IsString()
  threadId: string;

  @ApiPropertyOptional({
    description: 'Optional message ID to reply to (Facebook Messenger-style flat reply)',
    example: 'deac627b-8be6-4d15-a050-f2074cdbbc55',
  })
  @IsOptional()
  @IsUUID()
  replyToMessageId?: string;

  @ApiPropertyOptional({
    description: 'Optional message ID for editing/modifying existing message',
    example: 'deac627b-8be6-4d15-a050-f2074cdbbc55',
  })
  @IsOptional()
  @IsUUID()
  messageId?: string;

  @ApiPropertyOptional({
    description:
      'Whether to always approve tool calls without user confirmation',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  alwaysApproveToolCall?: boolean;

  @ApiPropertyOptional({
    description:
      'Array of attachment context for files and images referenced in the message',
    type: 'array',
    items: {
      oneOf: [
        { $ref: '#/components/schemas/FileAttachmentContextDto' },
        { $ref: '#/components/schemas/ImageAttachmentContextDto' },
      ],
    },
    example: [
      {
        type: 'file',
        fileId: 'f_a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      },
      {
        type: 'image',
        imageId: 'img_b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e',
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object, {
    keepDiscriminatorProperty: true,
    discriminator: {
      property: 'type',
      subTypes: [
        { value: FileAttachmentContextDto, name: 'file' },
        { value: ImageAttachmentContextDto, name: 'image' },
      ],
    },
  })
  attachmentContext?: AttachmentContextArrayDto;
}

export function OnlyOneToolCallDecisionBlockAllowed(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'OnlyOneToolCallDecisionBlockAllowed',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any[], args: ValidationArguments) {
          if (!Array.isArray(value)) return false;

          const toolCallBlocks = value.filter(
            (block) => block?.type === 'tool_call_decision',
          );

          if (toolCallBlocks.length > 1) return false;

          if (toolCallBlocks.length === 1 && value.length > 1) return false;

          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `If a "tool_call_decision" block exists, it must be the only block. Only one such block is allowed.`;
        },
      },
    });
  };
}
