/**
 * TypeScript interfaces và types cho Chat Streaming System
 * Hỗ trợ Server-Sent Events (SSE) và REST API cho chat real-time
 */

/**
 * Trạng thái kết nối SSE
 */
export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * Trạng thái tin nhắn
 */
export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  STREAMING = 'streaming',
  COMPLETED = 'completed',
  ERROR = 'error'
}

/**
 * Loại người gửi tin nhắn - hỗ trợ thêm supervisor và worker roles
 */
export type MessageSender = 'user' | 'ai' | 'supervisor' | 'worker';

/**
 * Loại content block
 */
export type ContentBlockType = 'text' | 'image' | 'file';

/**
 * Loại SSE event
 */
export type SSEEventType =
  | 'connected'
  | 'stream_text_token'
  | 'stream_tool_token'
  | 'llm_stream_end'
  | 'end'
  | 'error';

/**
 * Interface cho content block trong tin nhắn
 */
export interface ContentBlock {
  /**
   * Loại content
   */
  type: ContentBlockType;

  /**
   * Nội dung text hoặc URL cho file/image
   */
  content: string;

  /**
   * Metadata bổ sung (tùy chọn)
   */
  metadata?: {
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
    alt?: string;
  };
}

/**
 * Interface cho tin nhắn chat
 */
export interface ChatMessage {
  /**
   * ID duy nhất của tin nhắn
   */
  id: string;

  /**
   * Nội dung tin nhắn (text hoặc content blocks)
   */
  content: string | ContentBlock[];

  /**
   * Người gửi tin nhắn
   */
  sender: MessageSender;

  /**
   * Thời gian tạo tin nhắn
   */
  timestamp: Date;

  /**
   * Trạng thái tin nhắn
   */
  status: MessageStatus;

  /**
   * Thread ID mà tin nhắn thuộc về
   */
  threadId: string;

  /**
   * Run ID cho việc tracking (tùy chọn)
   */
  runId?: string;

  /**
   * Message ID từ API (cho supervisor messages)
   */
  messageId?: string;

  /**
   * Metadata bổ sung
   */
  metadata?: {
    agentId?: string;
    model?: string;
    tokens?: number;
    processingTime?: number;
    /**
     * Đánh dấu message này từ history hay real-time
     */
    isFromHistory?: boolean;
    /**
     * Đánh dấu message này từ API mới (ThreadMessageResponseDto)
     */
    isFromNewAPI?: boolean;
    /**
     * Đánh dấu message này có thể collapse (cho worker messages)
     */
    collapsed?: boolean;
    /**
     * Message ID từ API response
     */
    apiMessageId?: string;
    /**
     * Content preview từ message_created event
     */
    contentPreview?: string;
    /**
     * Đánh dấu message được ẩn sau delay (cho worker messages)
     */
    hideAfterDelay?: boolean;
    /**
     * Đánh dấu message đang trong quá trình ẩn với animation
     */
    isHiding?: boolean;
    /**
     * ID của message được reply (từ API mới)
     */
    replyToMessageId?: string;
    /**
     * Content của message được reply (từ API mới)
     */
    replyContent?: string;
    /**
     * ID của message được modify (từ API mới)
     */
    modifiedMessageId?: string;
    /**
     * Context cho attachments (từ API mới)
     */
    attachmentContext?: ChatAttachmentContext[];
    /**
     * Thời gian cập nhật message (từ API mới)
     */
    updatedAt?: Date;
  };
}

/**
 * Interface cho SSE event data - API mới
 * Endpoint: GET /v1/chat/stream/events/{threadId}/{runId}
 */
export interface SSEEventData {
  /**
   * Loại event
   */
  event: SSEEventType;

  /**
   * Dữ liệu event
   */
  data: {
    /**
     * Role của người gửi (supervisor, assistant, etc.)
     */
    role?: string;

    /**
     * Text token (cho stream_text_token)
     */
    text?: string;

    /**
     * Thread ID
     */
    threadId?: string;

    /**
     * Run ID
     */
    runId?: string;

    /**
     * Trạng thái kết nối (cho connected event)
     */
    status?: string;

    /**
     * Mode (cho connected event)
     */
    mode?: string;

    /**
     * From (cho connected event)
     */
    from?: string;
  };

  /**
   * Timestamp của event
   */
  timestamp: number;

  /**
   * ID của event (tùy chọn)
   */
  id?: string;
}

/**
 * Interface cho SSE connection configuration - API mới
 */
export interface SSEConnectionConfig {
  /**
   * Thread ID
   */
  threadId: string;

  /**
   * Run ID
   */
  runId: string;

  /**
   * Base URL cho SSE
   */
  sseBaseUrl?: string;

  /**
   * Auth token
   */
  authToken?: string;

  /**
   * Timeout cho connection (ms)
   */
  timeout?: number;

  /**
   * Có tự động reconnect không
   */
  autoReconnect?: boolean;
}

/**
 * Interface cho request gửi tin nhắn - API cũ (legacy) - DEPRECATED
 * Sử dụng ChatMessageRequest thay thế
 * @deprecated Use ChatMessageRequest instead
 */
export interface SendMessageRequest {
  /**
   * Các content blocks của tin nhắn
   */
  contentBlocks: ContentBlock[];

  /**
   * Thread ID để group các tin nhắn
   */
  threadId: string;

  /**
   * Tự động approve tool calls
   */
  alwaysApproveToolCall: boolean;

  /**
   * Metadata bổ sung (tùy chọn)
   */
  metadata?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
  };
}

/**
 * Content block cho tin nhắn chat
 */
export interface ChatContentBlock {
  type: 'text' | 'image' | 'file' | 'tool_call_decision';
  content?: string;
  imageId?: string;
  fileId?: string;
  decision?: 'yes' | 'no' | 'always'; // Cho tool_call_decision
  [key: string]: unknown;
}

/**
 * Attachment context cho tin nhắn
 */
export interface ChatAttachmentContext {
  type: 'file' | 'image';
  fileId?: string;
  imageId?: string;
}

/**
 * Interface chuẩn duy nhất cho request gửi tin nhắn chat
 * Hỗ trợ tất cả các tính năng: text, reply, modify, attachments, tool calls
 * Endpoint: POST /v1/chat/message
 *
 * @example
 * // Tin nhắn text đơn giản
 * const simpleMessage: ChatMessageRequest = {
 *   contentBlocks: [{ type: 'text', content: 'Hello, how can I help you?' }],
 *   threadId: 'deac627b-8be6-4d15-a050-f2074cdbbc55'
 * };
 *
 * // Reply tin nhắn (Facebook Messenger-style)
 * const replyMessage: ChatMessageRequest = {
 *   contentBlocks: [{ type: 'text', content: 'Thanks for the information!' }],
 *   replyToMessageId: 'deac627b-8be6-4d15-a050-f2074cdbbc55',
 *   threadId: 'deac627b-8be6-4d15-a050-f2074cdbbc55'
 * };
 *
 * // Tin nhắn với attachments
 * const messageWithAttachments: ChatMessageRequest = {
 *   contentBlocks: [
 *     { type: 'text', content: 'Here are the files you requested:' },
 *     { type: 'image', imageId: 'deac627b-8be6-4d15-a050-f2074cdbbc55' },
 *     { type: 'file', fileId: 'deac627b-8be6-4d15-a050-f2074cdbbc55' }
 *   ],
 *   threadId: 'deac627b-8be6-4d15-a050-f2074cdbbc55',
 *   attachmentContext: [
 *     { type: 'image', fileId: 'deac627b-8be6-4d15-a050-f2074cdbbc55' },
 *     { type: 'file', fileId: 'deac627b-8be6-4d15-a050-f2074cdbbc55' }
 *   ]
 * };
 *
 * // Modify tin nhắn (với cascade delete)
 * const modifyMessage: ChatMessageRequest = {
 *   contentBlocks: [
 *     { type: 'text', content: 'Sorry, I meant to say: The meeting is at 3 PM, not 2 PM.' },
 *     { type: 'file', fileId: 'deac627b-8be6-4d15-a050-f2074cdbbc55' }
 *   ],
 *   messageId: 'deac627b-8be6-4d15-a050-f2074cdbbc55',
 *   threadId: 'deac627b-8be6-4d15-a050-f2074cdbbc55',
 *   attachmentContext: [
 *     { type: 'file', fileId: 'deac627b-8be6-4d15-a050-f2074cdbbc55' }
 *   ]
 * };
 *
 * // Tool call decisions
 * const approveToolCall: ChatMessageRequest = {
 *   contentBlocks: [{ type: 'tool_call_decision', decision: 'yes' }],
 *   threadId: 'deac627b-8be6-4d15-a050-f2074cdbbc55'
 * };
 *
 * const alwaysApproveToolCall: ChatMessageRequest = {
 *   contentBlocks: [{ type: 'tool_call_decision', decision: 'always' }],
 *   threadId: 'deac627b-8be6-4d15-a050-f2074cdbbc55',
 *   alwaysApproveToolCall: true
 * };
 */
export interface ChatMessageRequest {
  /**
   * Các content blocks của tin nhắn
   * Hỗ trợ: text, image, file, tool_call_decision
   */
  contentBlocks: ChatContentBlock[];

  /**
   * Thread ID để group các tin nhắn (bắt buộc)
   */
  threadId: string;

  /**
   * Message ID để reply (Facebook Messenger-style flat reply)
   * Không thể dùng cùng với messageId
   */
  replyToMessageId?: string;

  /**
   * Message ID để edit/modify tin nhắn hiện có
   * Khi sử dụng, tất cả tin nhắn sau sẽ bị xóa (cascade delete)
   * Không thể dùng cùng với replyToMessageId
   */
  messageId?: string;

  /**
   * Tự động approve tool calls
   * Bắt buộc true khi decision = 'always'
   */
  alwaysApproveToolCall?: boolean;

  /**
   * Context cho attachments (file và image)
   * Cung cấp metadata bổ sung cho các attachment
   */
  attachmentContext?: ChatAttachmentContext[];
}

/**
 * Interface cho response gửi tin nhắn - API cũ (legacy) - DEPRECATED
 * Sử dụng ChatMessageResponse thay thế
 * @deprecated Use ChatMessageResponse instead
 */
export interface SendMessageResponse {
  /**
   * Run ID được tạo
   */
  runId: string;

  /**
   * Agent ID xử lý
   */
  agentId: string;

  /**
   * Tên agent
   */
  agentName: string;

  /**
   * Trạng thái run
   */
  status: string;

  /**
   * Thời gian tạo (timestamp)
   */
  createdAt: number;

  /**
   * Thông báo chi tiết
   */
  message: string;
};

/**
 * Chi tiết về modification khi modify message
 */
export interface ChatModificationDetails {
  /**
   * ID của message được modify
   */
  modifiedMessageId: string;

  /**
   * Array các message ID bị xóa do cascade delete
   */
  deletedMessageIds: string[];

  /**
   * Tổng số message bị xóa
   */
  deletedMessagesCount: number;

  /**
   * Loại operation được thực hiện
   */
  operationType: 'modify_text' | 'modify_content' | 'modify_attachments';
}

/**
 * Interface chuẩn duy nhất cho response gửi tin nhắn chat
 * Response từ POST /v1/chat/message
 * Hỗ trợ tất cả các trường hợp: regular, modify, mixed requests
 *
 * @example
 * // Regular message response
 * {
 *   messageId: '0280c24b-c849-492d-b5fd-e1c927186272',
 *   runId: 'run_123456-789-abc',
 *   agentName: 'Customer Support Agent',
 *   status: 'created',
 *   createdAt: 1672531200000,
 *   modificationDetails: {
 *     modifiedMessageId: '',
 *     deletedMessageIds: [],
 *     deletedMessagesCount: 0
 *   }
 * }
 *
 * // Modify-only response
 * {
 *   messageId: 'msg_123_modified',
 *   runId: 'run_456',
 *   agentName: 'Assistant',
 *   status: 'created',
 *   createdAt: 1749708424552,
 *   modificationDetails: {
 *     modifiedMessageId: 'msg_123',
 *     deletedMessageIds: ['msg_124', 'msg_125'],
 *     deletedMessagesCount: 2
 *   }
 * }
 */
export interface ChatMessageResponse {
  /**
   * Message ID được tạo hoặc modified
   */
  messageId: string;

  /**
   * Run ID được tạo để xử lý message
   */
  runId: string;

  /**
   * Tên agent xử lý message
   */
  agentName: string;

  /**
   * Trạng thái run (thường là 'created')
   */
  status: string;

  /**
   * Thời gian tạo (timestamp)
   */
  createdAt: number;

  /**
   * Chi tiết về modification (luôn có, có thể empty cho regular messages)
   */
  modificationDetails?: ChatModificationDetails;
}

// ===== TYPE ALIASES FOR BACKWARD COMPATIBILITY =====

/**
 * Alias cho ChatMessageRequest để backward compatibility
 * @deprecated Use ChatMessageRequest instead
 */
export type MessageRequest = ChatMessageRequest;

/**
 * Alias cho ChatMessageResponse để backward compatibility
 * @deprecated Use ChatMessageResponse instead
 */
export type MessageResponse = ChatMessageResponse;

/**
 * Alias cho ChatContentBlock để backward compatibility
 * @deprecated Use ChatContentBlock instead
 */
export type MessageContentBlock = ChatContentBlock;

/**
 * Alias cho ChatAttachmentContext để backward compatibility
 * @deprecated Use ChatAttachmentContext instead
 */
export type MessageAttachmentContext = ChatAttachmentContext;

/**
 * Alias cho ChatModificationDetails để backward compatibility
 * @deprecated Use ChatModificationDetails instead
 */
export type MessageModificationDetails = ChatModificationDetails;

/**
 * Interface cho request dừng chat run
 */
export interface StopRunRequest {
  /**
   * Run ID cần dừng
   */
  runId: string;
}

/**
 * Interface cho response dừng chat run
 * Response từ DELETE /v1/chat/runs/{runId}
 */
export interface StopRunResponse {
  /**
   * Mã response
   */
  code: number;

  /**
   * Thông báo
   */
  message: string;

  /**
   * Kết quả
   */
  result?: {
    /**
     * Run ID đã dừng
     */
    runId: string;

    /**
     * Trạng thái sau khi dừng
     */
    status: string;
  };
}

/**
 * Interface cho Run Management
 */
export interface ChatRun {
  /**
   * Run ID duy nhất
   */
  runId: string;

  /**
   * Thread ID liên quan
   */
  threadId: string;

  /**
   * Agent ID xử lý
   */
  agentId: string;

  /**
   * Trạng thái run
   */
  status: 'created' | 'running' | 'streaming' | 'completed' | 'stopped' | 'error';

  /**
   * Thời gian tạo
   */
  createdAt: Date;

  /**
   * Thời gian bắt đầu streaming
   */
  startedAt?: Date;

  /**
   * Thời gian hoàn thành
   */
  completedAt?: Date;

  /**
   * Metadata run
   */
  metadata?: {
    agentName?: string;
    messageId?: string;
    tokensGenerated?: number;
    processingTime?: number;
  };
}

/**
 * Interface cho cấu hình SSE connection
 */
export interface SSEConnectionConfig {
  /**
   * Base URL của API
   */
  baseUrl: string;

  /**
   * Thread ID để kết nối
   */
  threadId: string;

  /**
   * Headers bổ sung (authorization, etc.)
   */
  headers?: Record<string, string>;

  /**
   * Timeout cho connection (ms)
   */
  timeout?: number;

  /**
   * Số lần retry khi connection failed
   */
  maxRetries?: number;

  /**
   * Delay giữa các lần retry (ms)
   */
  retryDelay?: number;

  /**
   * Có tự động reconnect hay không
   */
  autoReconnect?: boolean;
}

/**
 * Interface cho chat session - Cập nhật cho API mới
 */
export interface ChatSession {
  /**
   * Thread ID duy nhất
   */
  threadId: string;

  /**
   * Agent ID đang sử dụng
   */
  agentId: string;

  /**
   * Danh sách tin nhắn
   */
  messages: ChatMessage[];

  /**
   * Run ID hiện tại (nếu đang chạy)
   */
  currentRunId?: string;

  /**
   * Danh sách runs đã thực hiện
   */
  runs: ChatRun[];

  /**
   * Trạng thái session
   */
  status: 'active' | 'idle' | 'streaming' | 'error';

  /**
   * Thời gian tạo session
   */
  createdAt: Date;

  /**
   * Thời gian cập nhật cuối
   */
  updatedAt: Date;

  /**
   * Metadata session
   */
  metadata?: {
    title?: string;
    description?: string;
    tags?: string[];
    lastAgentName?: string;
  };
}

/**
 * Interface cho streaming state
 */
export interface StreamingState {
  /**
   * Có đang streaming hay không
   */
  isStreaming: boolean;

  /**
   * Text đang được stream
   */
  currentText: string;

  /**
   * Message ID đang được stream
   */
  streamingMessageId?: string;

  /**
   * Run ID đang stream
   */
  streamingRunId?: string;

  /**
   * Số tokens đã nhận
   */
  tokensReceived: number;

  /**
   * Thời gian bắt đầu stream
   */
  startTime?: Date;
}

/**
 * Interface cho error handling
 */
export interface ChatError {
  /**
   * Mã lỗi
   */
  code: string;

  /**
   * Thông báo lỗi
   */
  message: string;

  /**
   * Chi tiết lỗi
   */
  details?: unknown;

  /**
   * Thời gian xảy ra lỗi
   */
  timestamp: Date;

  /**
   * Context khi lỗi xảy ra
   */
  context?: {
    threadId?: string;
    runId?: string;
    messageId?: string;
    action?: string;
    eventData?: unknown;
    [key: string]: unknown;
  };
}

/**
 * Utility types cho luồng chat mới
 */

/**
 * Chat flow step - 8 bước của luồng chat mới
 */
export type ChatFlowStep =
  | 'input'           // 1. Nhập tin nhắn
  | 'display_user'    // 2. Hiển thị tin nhắn user
  | 'show_loading'    // 3. Hiển thị loading assistant
  | 'send_api'        // 4. Send POST API
  | 'stop_previous'   // 5. Stop stream trước (nếu có)
  | 'connect_sse'     // 6. Kết nối SSE
  | 'stream_text'     // 7. Stream text từng từ
  | 'disconnect_sse'; // 8. Disconnect SSE

/**
 * Utility functions cho message conversion
 */

/**
 * Map role từ SSE event thành MessageSender
 *
 * IMPORTANT: Để tránh message splitting, tất cả AI roles (supervisor, assistant)
 * đều được map thành 'ai'. Chỉ worker và user được giữ nguyên.
 */
export function mapRoleToMessageSender(role?: string): MessageSender {
  if (!role) return 'ai';

  switch (role.toLowerCase()) {
    case 'worker':
      return 'worker';
    case 'user':
      return 'user';
    case 'supervisor':
    case 'assistant':
    case 'ai':
    default:
      return 'ai'; // ✅ Tất cả AI roles đều map thành 'ai'
  }
}

/**
 * Convert HistoryMessage từ API thành ChatMessage cho UI
 */
export function convertHistoryMessageToChatMessage(historyMessage: HistoryMessage): ChatMessage {
  console.log('[convertHistoryMessageToChatMessage] Converting:', historyMessage);

  // Lấy text content từ contentBlocks array
  const textContent = historyMessage.content?.contentBlocks
    ?.find(block => block.type === 'text')?.content || '';

  const chatMessage = {
    id: historyMessage.messageId,
    content: textContent,
    sender: historyMessage.role === 'user' ? 'user' : 'ai',
    timestamp: new Date(historyMessage.timestamp),
    status: MessageStatus.COMPLETED,
    threadId: historyMessage.threadId,
    runId: historyMessage.content?.runId,
    metadata: {
      // Đánh dấu đây là message từ history
      isFromHistory: true,
      agentId: historyMessage.content?.agentId,
      tokens: historyMessage.content?.tokenCount,
      complete: historyMessage.content?.complete
    }
  } as ChatMessage;

  console.log('[convertHistoryMessageToChatMessage] Result:', chatMessage);
  return chatMessage;
}

/**
 * Convert array HistoryMessage thành array ChatMessage
 */
export function convertHistoryMessagesToChatMessages(historyMessages: HistoryMessage[]): ChatMessage[] {
  return historyMessages.map(convertHistoryMessageToChatMessage);
}

/**
 * Convert ThreadMessageResponseDto từ API mới thành ChatMessage cho UI
 */
export function convertThreadMessageToChatMessage(
  threadMessage: ThreadMessageResponseDto,
  allMessages?: ThreadMessageResponseDto[]
): ChatMessage {
  console.log('[convertThreadMessageToChatMessage] Converting:', threadMessage);

  // Lấy text content từ contentBlocks array
  const textContent = threadMessage.content.contentBlocks
    ?.find(block => block.type === 'text')?.content || '';

  // Xử lý reply information từ content
  const replyToMessageId = threadMessage.content.replyToMessageId;
  const messageId = threadMessage.content.messageId; // Cho modify messages

  // Tạo metadata object với conditional properties
  const metadata: ChatMessage['metadata'] = {
    // Đánh dấu đây là message từ API mới
    isFromNewAPI: true
  };

  // Chỉ thêm các field khi chúng có giá trị
  if (replyToMessageId) {
    metadata.replyToMessageId = replyToMessageId;

    // Tìm reply content từ allMessages nếu có
    if (allMessages) {
      const replyMessage = allMessages.find(msg => msg.messageId === replyToMessageId);
      if (replyMessage) {
        const replyContent = replyMessage.content.contentBlocks
          ?.find(block => block.type === 'text')?.content || '';
        if (replyContent) {
          metadata.replyContent = replyContent;
        }
      }
    }
  }

  if (messageId) {
    metadata.modifiedMessageId = messageId;
  }

  if (threadMessage.content.attachmentContext && threadMessage.content.attachmentContext.length > 0) {
    metadata.attachmentContext = threadMessage.content.attachmentContext;
  }

  if (threadMessage.updatedAt) {
    metadata.updatedAt = new Date(threadMessage.updatedAt);
  }

  const chatMessage: ChatMessage = {
    id: threadMessage.messageId,
    content: textContent,
    sender: threadMessage.role === 'user' ? 'user' : 'ai',
    timestamp: new Date(threadMessage.timestamp),
    status: MessageStatus.COMPLETED,
    threadId: threadMessage.threadId,
    metadata
  };

  console.log('[convertThreadMessageToChatMessage] Result:', chatMessage);
  return chatMessage;
}

/**
 * Convert array ThreadMessageResponseDto thành array ChatMessage
 */
export function convertThreadMessagesToChatMessages(threadMessages: ThreadMessageResponseDto[]): ChatMessage[] {
  return threadMessages.map(threadMessage =>
    convertThreadMessageToChatMessage(threadMessage, threadMessages)
  );
}

/**
 * Chat flow state
 */
export interface ChatFlowState {
  /**
   * Bước hiện tại
   */
  currentStep: ChatFlowStep;

  /**
   * Có đang xử lý không
   */
  isProcessing: boolean;

  /**
   * Lỗi trong quá trình xử lý
   */
  error?: ChatError;

  /**
   * Metadata cho bước hiện tại
   */
  stepMetadata?: {
    startTime?: Date;
    duration?: number;
    data?: unknown;
  };
}

/**
 * Message send options cho API mới
 */
export interface MessageSendOptions {
  /**
   * Nội dung tin nhắn
   */
  content: string | ContentBlock[];

  /**
   * Thread ID
   */
  threadId: string;

  /**
   * Có tự động approve tool calls không
   */
  alwaysApproveToolCall?: boolean;

  /**
   * Có dừng stream trước đó không
   */
  stopPreviousRun?: boolean;

  /**
   * Timeout cho operation (ms)
   */
  timeout?: number;
}

/**
 * Request để tạo thread chat
 */
export interface CreateThreadRequest {
  /**
   * Tên thread
   */
  name: string;
}

/**
 * Response từ API tạo thread
 */
export interface CreateThreadResponse {

  /**
   * Thread ID được tạo
   */
  threadId: string;

  /**
   * Tên thread
   */
  name: string;

  /**
   * Thời gian tạo (timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật (timestamp)
   */
  updatedAt: number;
};

/**
 * Thread item trong danh sách
 */
export interface ThreadItem {
  /**
   * Thread ID
   */
  threadId: string;

  /**
   * Tên thread
   */
  name: string;

  /**
   * Thời gian tạo (timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật (timestamp)
   */
  updatedAt: number;
}

/**
 * Query parameters cho API lấy threads
 */
export interface GetThreadsQuery {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng items per page
   */
  limit?: number;

  /**
   * Sắp xếp theo field nào
   */
  sortBy?: string;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;
}

/**
 * Response từ API lấy threads
 */
export interface GetThreadsResponse {
  /**
   * Danh sách threads
   */
  items: ThreadItem[];

  /**
   * Metadata pagination
   */
  meta: PaginationMeta;

  /**
   * Tổng số items (deprecated - sử dụng meta.totalItems)
   */
  total?: number;

  /**
   * Trang hiện tại (deprecated - sử dụng meta.currentPage)
   */
  page?: number;

  /**
   * Số items per page (deprecated - sử dụng meta.itemsPerPage)
   */
  limit?: number;
};

/**
 * Response từ API lấy chi tiết thread
 */
export interface ThreadDetailResponse {
  /**
   * Thread ID
   */
  threadId: string;

  /**
   * Tên thread
   */
  name: string;

  /**
   * Thời gian tạo (timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật (timestamp)
   */
  updatedAt: number;
}

/**
 * Thread Message Response DTO - Cấu trúc mới từ API
 * Exact same structure as MessageRequestDto (input = output principle)
 */
export interface ThreadMessageResponseDto {
  /**
   * Unique message identifier
   */
  messageId: string;

  /**
   * Thread ID this message belongs to
   */
  threadId: string;

  /**
   * Role of the message sender
   */
  role: 'user' | 'assistant';

  /**
   * Message content - exact same structure as ChatMessageRequest
   */
  content: ChatMessageRequest;

  /**
   * Message timestamp (epoch milliseconds)
   */
  timestamp: number;

  /**
   * Message last updated timestamp (epoch milliseconds)
   * Same as timestamp if never modified
   */
  updatedAt?: number;
}

/**
 * Request body cho cập nhật thread
 */
export interface UpdateThreadRequest {
  /**
   * Tên thread mới
   */
  name: string;
}

/**
 * Content block trong message history từ API
 */
export interface HistoryContentBlock {
  /**
   * Loại content
   */
  type: 'text' | 'image' | 'file';

  /**
   * Nội dung của content block
   */
  content: string;
}

/**
 * Content structure trong message history từ API
 */
export interface HistoryMessageContent {
  /**
   * Có hoàn thành hay không (cho assistant messages)
   */
  complete?: boolean;

  /**
   * Timestamp của content
   */
  timestamp?: number;

  /**
   * Số lượng tokens (cho assistant messages)
   */
  tokenCount?: number;

  /**
   * Danh sách content blocks
   */
  contentBlocks: HistoryContentBlock[];

  /**
   * Run ID liên quan (cho user messages)
   */
  runId?: string;

  /**
   * Agent ID xử lý (cho user messages)
   */
  agentId?: string;
}

/**
 * Message từ API history - format từ server (cập nhật theo response thực tế)
 */
export interface HistoryMessage {
  /**
   * ID của message
   */
  messageId: string;

  /**
   * ID của thread chứa message
   */
  threadId: string;

  /**
   * Vai trò của người gửi
   */
  role: 'user' | 'assistant';

  /**
   * Nội dung message với cấu trúc phức tạp
   */
  content: HistoryMessageContent;

  /**
   * Timestamp (Unix timestamp in milliseconds)
   */
  timestamp: number;
}

/**
 * Query parameters cho API lấy lịch sử messages
 */
export interface GetMessagesQuery {
  /**
   * Trang hiện tại (default: 1)
   */
  page?: number;

  /**
   * Số lượng items per page (default: 50)
   */
  limit?: number;

  /**
   * Sắp xếp theo field nào (default: 'createdAt')
   */
  sortBy?: string;

  /**
   * Hướng sắp xếp (default: 'DESC')
   */
  sortDirection?: 'ASC' | 'DESC';

  /**
   * Filter theo role (optional)
   */
  role?: 'user' | 'assistant';
}

/**
 * Metadata cho pagination response
 */
export interface PaginationMeta {
  /**
   * Tổng số items
   */
  totalItems: number;

  /**
   * Số items trong page hiện tại
   */
  itemCount: number;

  /**
   * Số items per page
   */
  itemsPerPage: number;

  /**
   * Tổng số pages
   */
  totalPages: number;

  /**
   * Page hiện tại
   */
  currentPage: number;

  /**
   * Có items hay không
   */
  hasItems: boolean;
}

/**
 * Response data từ API lấy lịch sử messages (API mới)
 */
export interface GetMessagesResponseData {
  /**
   * Danh sách messages với format mới
   */
  items: ThreadMessageResponseDto[];

  /**
   * Metadata pagination
   */
  meta: PaginationMeta;
}

/**
 * Response data từ API lấy lịch sử messages (Legacy)
 * @deprecated Use GetMessagesResponseData with ThreadMessageResponseDto instead
 */
export interface GetMessagesResponseDataLegacy {
  /**
   * Danh sách messages với format cũ
   */
  items: HistoryMessage[];

  /**
   * Metadata pagination
   */
  meta: PaginationMeta;
}