/**
 * TypeScript interfaces và types cho Chat Streaming System
 * Hỗ trợ Server-Sent Events (SSE) và REST API cho chat real-time
 */

/**
 * Trạng thái kết nối SSE
 */
export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * Trạng thái tin nhắn
 */
export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  STREAMING = 'streaming',
  COMPLETED = 'completed',
  ERROR = 'error'
}

/**
 * Loại người gửi tin nhắn - hỗ trợ thêm supervisor và worker roles
 */
export type MessageSender = 'user' | 'ai' | 'supervisor' | 'worker';

/**
 * Loại content block
 */
export type ContentBlockType = 'text' | 'image' | 'file';

/**
 * Loại SSE event
 */
export type SSEEventType =
  | 'connected'
  | 'stream_text_token'
  | 'stream_tool_token'
  | 'llm_stream_end'
  | 'end'
  | 'error';

/**
 * Interface cho content block trong tin nhắn
 */
export interface ContentBlock {
  /**
   * Loại content
   */
  type: ContentBlockType;

  /**
   * Nội dung text hoặc URL cho file/image
   */
  content: string;

  /**
   * Metadata bổ sung (tùy chọn)
   */
  metadata?: {
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
    alt?: string;
  };
}

/**
 * Interface cho tin nhắn chat
 */
export interface ChatMessage {
  /**
   * ID duy nhất của tin nhắn
   */
  id: string;

  /**
   * Nội dung tin nhắn (text hoặc content blocks)
   */
  content: string | ContentBlock[];

  /**
   * Người gửi tin nhắn
   */
  sender: MessageSender;

  /**
   * Thời gian tạo tin nhắn
   */
  timestamp: Date;

  /**
   * Trạng thái tin nhắn
   */
  status: MessageStatus;

  /**
   * Thread ID mà tin nhắn thuộc về
   */
  threadId: string;

  /**
   * Run ID cho việc tracking (tùy chọn)
   */
  runId?: string;

  /**
   * Message ID từ API (cho supervisor messages)
   */
  messageId?: string;

  /**
   * Metadata bổ sung
   */
  metadata?: {
    agentId?: string;
    model?: string;
    tokens?: number;
    processingTime?: number;
    /**
     * Đánh dấu message này từ history hay real-time
     */
    isFromHistory?: boolean;
    /**
     * Đánh dấu message này có thể collapse (cho worker messages)
     */
    collapsed?: boolean;
    /**
     * Message ID từ API response
     */
    apiMessageId?: string;
    /**
     * Content preview từ message_created event
     */
    contentPreview?: string;
    /**
     * Đánh dấu message được ẩn sau delay (cho worker messages)
     */
    hideAfterDelay?: boolean;
    /**
     * Đánh dấu message đang trong quá trình ẩn với animation
     */
    isHiding?: boolean;
  };
}

/**
 * Interface cho SSE event data - API mới
 * Endpoint: GET /v1/chat/stream/events/{threadId}/{runId}
 */
export interface SSEEventData {
  /**
   * Loại event
   */
  event: SSEEventType;

  /**
   * Dữ liệu event
   */
  data: {
    /**
     * Role của người gửi (supervisor, assistant, etc.)
     */
    role?: string;

    /**
     * Text token (cho stream_text_token)
     */
    text?: string;

    /**
     * Thread ID
     */
    threadId?: string;

    /**
     * Run ID
     */
    runId?: string;

    /**
     * Trạng thái kết nối (cho connected event)
     */
    status?: string;

    /**
     * Mode (cho connected event)
     */
    mode?: string;

    /**
     * From (cho connected event)
     */
    from?: string;
  };

  /**
   * Timestamp của event
   */
  timestamp: number;

  /**
   * ID của event (tùy chọn)
   */
  id?: string;
}

/**
 * Interface cho SSE connection configuration - API mới
 */
export interface SSEConnectionConfig {
  /**
   * Thread ID
   */
  threadId: string;

  /**
   * Run ID
   */
  runId: string;

  /**
   * Base URL cho SSE
   */
  sseBaseUrl?: string;

  /**
   * Auth token
   */
  authToken?: string;

  /**
   * Timeout cho connection (ms)
   */
  timeout?: number;

  /**
   * Có tự động reconnect không
   */
  autoReconnect?: boolean;
}

/**
 * Interface cho request gửi tin nhắn - API cũ (legacy)
 * Endpoint: POST /v1/chat/message
 */
export interface SendMessageRequest {
  /**
   * Các content blocks của tin nhắn
   */
  contentBlocks: ContentBlock[];

  /**
   * Thread ID để group các tin nhắn
   */
  threadId: string;

  /**
   * Tự động approve tool calls
   */
  alwaysApproveToolCall: boolean;

  /**
   * Metadata bổ sung (tùy chọn)
   */
  metadata?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
  };
}

/**
 * Interface cho request gửi tin nhắn mới - hỗ trợ reply, modify, attachments
 * Endpoint: POST /v1/chat/message
 */
export interface SendMessageRequestDto {
  /**
   * Các content blocks của tin nhắn
   */
  contentBlocks: Array<{
    type: 'text' | 'image' | 'file' | 'tool_call_decision';
    content?: string;
    imageId?: string;
    fileId?: string;
    [key: string]: unknown;
  }>;

  /**
   * Thread ID để group các tin nhắn
   */
  threadId: string;

  /**
   * Message ID để reply (Facebook Messenger-style flat reply)
   */
  replyToMessageId?: string;

  /**
   * Message ID để edit/modify tin nhắn hiện có
   */
  messageId?: string;

  /**
   * Tự động approve tool calls
   */
  alwaysApproveToolCall?: boolean;

  /**
   * Context cho attachments
   */
  attachmentContext?: Array<{
    type: 'file' | 'image';
    fileId?: string;
    imageId?: string;
  }>;
}

/**
 * Interface cho response gửi tin nhắn - API cũ (legacy)
 * Response từ POST /v1/chat/message
 */
export interface SendMessageResponse {
  /**
   * Run ID được tạo
   */
  runId: string;

  /**
   * Agent ID xử lý
   */
  agentId: string;

  /**
   * Tên agent
   */
  agentName: string;

  /**
   * Trạng thái run
   */
  status: string;

  /**
   * Thời gian tạo (timestamp)
   */
  createdAt: number;

  /**
   * Thông báo chi tiết
   */
  message: string;
};

/**
 * Interface cho response gửi tin nhắn mới - hỗ trợ modification details
 * Response từ POST /v1/chat/message
 */
export interface SendMessageResponseDto {
  /**
   * Message ID được tạo
   */
  messageId: string;

  /**
   * Run ID được tạo
   */
  runId: string;

  /**
   * Tên agent xử lý
   */
  agentName: string;

  /**
   * Trạng thái run
   */
  status: string;

  /**
   * Thời gian tạo (timestamp)
   */
  createdAt: number;

  /**
   * Chi tiết về modification (chỉ có khi modify message)
   */
  modificationDetails?: {
    modifiedMessageId: string;
    deletedMessageIds: string[];
    deletedMessagesCount: number;
    operationType: 'modify_text' | 'modify_content' | 'modify_attachments';
  };
};

/**
 * Interface cho request dừng chat run
 */
export interface StopRunRequest {
  /**
   * Run ID cần dừng
   */
  runId: string;
}

/**
 * Interface cho response dừng chat run
 * Response từ DELETE /v1/chat/runs/{runId}
 */
export interface StopRunResponse {
  /**
   * Mã response
   */
  code: number;

  /**
   * Thông báo
   */
  message: string;

  /**
   * Kết quả
   */
  result?: {
    /**
     * Run ID đã dừng
     */
    runId: string;

    /**
     * Trạng thái sau khi dừng
     */
    status: string;
  };
}

/**
 * Interface cho Run Management
 */
export interface ChatRun {
  /**
   * Run ID duy nhất
   */
  runId: string;

  /**
   * Thread ID liên quan
   */
  threadId: string;

  /**
   * Agent ID xử lý
   */
  agentId: string;

  /**
   * Trạng thái run
   */
  status: 'created' | 'running' | 'streaming' | 'completed' | 'stopped' | 'error';

  /**
   * Thời gian tạo
   */
  createdAt: Date;

  /**
   * Thời gian bắt đầu streaming
   */
  startedAt?: Date;

  /**
   * Thời gian hoàn thành
   */
  completedAt?: Date;

  /**
   * Metadata run
   */
  metadata?: {
    agentName?: string;
    messageId?: string;
    tokensGenerated?: number;
    processingTime?: number;
  };
}

/**
 * Interface cho cấu hình SSE connection
 */
export interface SSEConnectionConfig {
  /**
   * Base URL của API
   */
  baseUrl: string;

  /**
   * Thread ID để kết nối
   */
  threadId: string;

  /**
   * Headers bổ sung (authorization, etc.)
   */
  headers?: Record<string, string>;

  /**
   * Timeout cho connection (ms)
   */
  timeout?: number;

  /**
   * Số lần retry khi connection failed
   */
  maxRetries?: number;

  /**
   * Delay giữa các lần retry (ms)
   */
  retryDelay?: number;

  /**
   * Có tự động reconnect hay không
   */
  autoReconnect?: boolean;
}

/**
 * Interface cho chat session - Cập nhật cho API mới
 */
export interface ChatSession {
  /**
   * Thread ID duy nhất
   */
  threadId: string;

  /**
   * Agent ID đang sử dụng
   */
  agentId: string;

  /**
   * Danh sách tin nhắn
   */
  messages: ChatMessage[];

  /**
   * Run ID hiện tại (nếu đang chạy)
   */
  currentRunId?: string;

  /**
   * Danh sách runs đã thực hiện
   */
  runs: ChatRun[];

  /**
   * Trạng thái session
   */
  status: 'active' | 'idle' | 'streaming' | 'error';

  /**
   * Thời gian tạo session
   */
  createdAt: Date;

  /**
   * Thời gian cập nhật cuối
   */
  updatedAt: Date;

  /**
   * Metadata session
   */
  metadata?: {
    title?: string;
    description?: string;
    tags?: string[];
    lastAgentName?: string;
  };
}

/**
 * Interface cho streaming state
 */
export interface StreamingState {
  /**
   * Có đang streaming hay không
   */
  isStreaming: boolean;

  /**
   * Text đang được stream
   */
  currentText: string;

  /**
   * Message ID đang được stream
   */
  streamingMessageId?: string;

  /**
   * Run ID đang stream
   */
  streamingRunId?: string;

  /**
   * Số tokens đã nhận
   */
  tokensReceived: number;

  /**
   * Thời gian bắt đầu stream
   */
  startTime?: Date;
}

/**
 * Interface cho error handling
 */
export interface ChatError {
  /**
   * Mã lỗi
   */
  code: string;

  /**
   * Thông báo lỗi
   */
  message: string;

  /**
   * Chi tiết lỗi
   */
  details?: unknown;

  /**
   * Thời gian xảy ra lỗi
   */
  timestamp: Date;

  /**
   * Context khi lỗi xảy ra
   */
  context?: {
    threadId?: string;
    runId?: string;
    messageId?: string;
    action?: string;
    eventData?: unknown;
    [key: string]: unknown;
  };
}

/**
 * Utility types cho luồng chat mới
 */

/**
 * Chat flow step - 8 bước của luồng chat mới
 */
export type ChatFlowStep =
  | 'input'           // 1. Nhập tin nhắn
  | 'display_user'    // 2. Hiển thị tin nhắn user
  | 'show_loading'    // 3. Hiển thị loading assistant
  | 'send_api'        // 4. Send POST API
  | 'stop_previous'   // 5. Stop stream trước (nếu có)
  | 'connect_sse'     // 6. Kết nối SSE
  | 'stream_text'     // 7. Stream text từng từ
  | 'disconnect_sse'; // 8. Disconnect SSE

/**
 * Utility functions cho message conversion
 */

/**
 * Map role từ SSE event thành MessageSender
 */
export function mapRoleToMessageSender(role?: string): MessageSender {
  if (!role) return 'ai';

  switch (role.toLowerCase()) {
    case 'supervisor':
      return 'supervisor';
    case 'worker':
      return 'worker';
    case 'user':
      return 'user';
    case 'assistant':
    case 'ai':
    default:
      return 'ai';
  }
}

/**
 * Convert HistoryMessage từ API thành ChatMessage cho UI
 */
export function convertHistoryMessageToChatMessage(historyMessage: HistoryMessage): ChatMessage {
  console.log('[convertHistoryMessageToChatMessage] Converting:', historyMessage);

  // Lấy text content từ contentBlocks array
  const textContent = historyMessage.content?.contentBlocks
    ?.find(block => block.type === 'text')?.content || '';

  const chatMessage = {
    id: historyMessage.messageId,
    content: textContent,
    sender: historyMessage.role === 'user' ? 'user' : 'ai',
    timestamp: new Date(historyMessage.timestamp),
    status: MessageStatus.COMPLETED,
    threadId: historyMessage.threadId,
    runId: historyMessage.content?.runId,
    metadata: {
      // Đánh dấu đây là message từ history
      isFromHistory: true,
      agentId: historyMessage.content?.agentId,
      tokens: historyMessage.content?.tokenCount,
      complete: historyMessage.content?.complete
    }
  } as ChatMessage;

  console.log('[convertHistoryMessageToChatMessage] Result:', chatMessage);
  return chatMessage;
}

/**
 * Convert array HistoryMessage thành array ChatMessage
 */
export function convertHistoryMessagesToChatMessages(historyMessages: HistoryMessage[]): ChatMessage[] {
  return historyMessages.map(convertHistoryMessageToChatMessage);
}

/**
 * Chat flow state
 */
export interface ChatFlowState {
  /**
   * Bước hiện tại
   */
  currentStep: ChatFlowStep;

  /**
   * Có đang xử lý không
   */
  isProcessing: boolean;

  /**
   * Lỗi trong quá trình xử lý
   */
  error?: ChatError;

  /**
   * Metadata cho bước hiện tại
   */
  stepMetadata?: {
    startTime?: Date;
    duration?: number;
    data?: unknown;
  };
}

/**
 * Message send options cho API mới
 */
export interface MessageSendOptions {
  /**
   * Nội dung tin nhắn
   */
  content: string | ContentBlock[];

  /**
   * Thread ID
   */
  threadId: string;

  /**
   * Có tự động approve tool calls không
   */
  alwaysApproveToolCall?: boolean;

  /**
   * Có dừng stream trước đó không
   */
  stopPreviousRun?: boolean;

  /**
   * Timeout cho operation (ms)
   */
  timeout?: number;
}

/**
 * Request để tạo thread chat
 */
export interface CreateThreadRequest {
  /**
   * Tên thread
   */
  name: string;
}

/**
 * Response từ API tạo thread
 */
export interface CreateThreadResponse {

  /**
   * Thread ID được tạo
   */
  threadId: string;

  /**
   * Tên thread
   */
  name: string;

  /**
   * Thời gian tạo (timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật (timestamp)
   */
  updatedAt: number;
};

/**
 * Thread item trong danh sách
 */
export interface ThreadItem {
  /**
   * Thread ID
   */
  threadId: string;

  /**
   * Tên thread
   */
  name: string;

  /**
   * Thời gian tạo (timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật (timestamp)
   */
  updatedAt: number;
}

/**
 * Query parameters cho API lấy threads
 */
export interface GetThreadsQuery {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng items per page
   */
  limit?: number;

  /**
   * Sắp xếp theo field nào
   */
  sortBy?: string;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;
}

/**
 * Response từ API lấy threads
 */
export interface GetThreadsResponse {
  /**
   * Danh sách threads
   */
  items: ThreadItem[];

  /**
   * Metadata pagination
   */
  meta: PaginationMeta;

  /**
   * Tổng số items (deprecated - sử dụng meta.totalItems)
   */
  total?: number;

  /**
   * Trang hiện tại (deprecated - sử dụng meta.currentPage)
   */
  page?: number;

  /**
   * Số items per page (deprecated - sử dụng meta.itemsPerPage)
   */
  limit?: number;
};

/**
 * Response từ API lấy chi tiết thread
 */
export interface ThreadDetailResponse {
  /**
   * Thread ID
   */
  threadId: string;

  /**
   * Tên thread
   */
  name: string;

  /**
   * Thời gian tạo (timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật (timestamp)
   */
  updatedAt: number;
}

/**
 * Request body cho cập nhật thread
 */
export interface UpdateThreadRequest {
  /**
   * Tên thread mới
   */
  name: string;
}

/**
 * Content block trong message history từ API
 */
export interface HistoryContentBlock {
  /**
   * Loại content
   */
  type: 'text' | 'image' | 'file';

  /**
   * Nội dung của content block
   */
  content: string;
}

/**
 * Content structure trong message history từ API
 */
export interface HistoryMessageContent {
  /**
   * Có hoàn thành hay không (cho assistant messages)
   */
  complete?: boolean;

  /**
   * Timestamp của content
   */
  timestamp?: number;

  /**
   * Số lượng tokens (cho assistant messages)
   */
  tokenCount?: number;

  /**
   * Danh sách content blocks
   */
  contentBlocks: HistoryContentBlock[];

  /**
   * Run ID liên quan (cho user messages)
   */
  runId?: string;

  /**
   * Agent ID xử lý (cho user messages)
   */
  agentId?: string;
}

/**
 * Message từ API history - format từ server (cập nhật theo response thực tế)
 */
export interface HistoryMessage {
  /**
   * ID của message
   */
  messageId: string;

  /**
   * ID của thread chứa message
   */
  threadId: string;

  /**
   * Vai trò của người gửi
   */
  role: 'user' | 'assistant';

  /**
   * Nội dung message với cấu trúc phức tạp
   */
  content: HistoryMessageContent;

  /**
   * Timestamp (Unix timestamp in milliseconds)
   */
  timestamp: number;
}

/**
 * Query parameters cho API lấy lịch sử messages
 */
export interface GetMessagesQuery {
  /**
   * Trang hiện tại (default: 1)
   */
  page?: number;

  /**
   * Số lượng items per page (default: 50)
   */
  limit?: number;

  /**
   * Sắp xếp theo field nào (default: 'createdAt')
   */
  sortBy?: string;

  /**
   * Hướng sắp xếp (default: 'DESC')
   */
  sortDirection?: 'ASC' | 'DESC';

  /**
   * Filter theo role (optional)
   */
  role?: 'user' | 'assistant';
}

/**
 * Metadata cho pagination response
 */
export interface PaginationMeta {
  /**
   * Tổng số items
   */
  totalItems: number;

  /**
   * Số items trong page hiện tại
   */
  itemCount: number;

  /**
   * Số items per page
   */
  itemsPerPage: number;

  /**
   * Tổng số pages
   */
  totalPages: number;

  /**
   * Page hiện tại
   */
  currentPage: number;

  /**
   * Có items hay không
   */
  hasItems: boolean;
}

/**
 * Response data từ API lấy lịch sử messages
 */
export interface GetMessagesResponseData {
  /**
   * Danh sách messages
   */
  items: HistoryMessage[];

  /**
   * Metadata pagination
   */
  meta: PaginationMeta;
}