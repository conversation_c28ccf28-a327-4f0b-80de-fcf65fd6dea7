/**
 * Chat API Service
 * Xử lý tất cả REST API calls cho chat system
 */

import { apiClient } from '@/shared/api';
import {
  SendMessageRequest,
  SendMessageResponse,
  StopRunResponse,
  ContentBlock,
  CreateThreadRequest,
  CreateThreadResponse,
  GetThreadsQuery,
  GetThreadsResponse,
  GetMessagesQuery,
  GetMessagesResponseData,
} from '@/shared/types/chat-streaming.types';
import { MessageRequestDto } from './dto/message-request.dto';
import { MessageResponseDto } from './dto/message-response.dto';

/**
 * Chat API Service Class
 */
export class ChatApiService {
  private baseUrl: string;
  private timeout: number;
  private debug: boolean;

  constructor(baseUrl: string, timeout: number = 30000, debug: boolean = false) {
    this.baseUrl = baseUrl;
    this.timeout = timeout;
    this.debug = debug;
  }

  /**
   * Log debug information
   */
  private log(message: string, data?: unknown): void {
    if (this.debug) {
      console.log(`[ChatApiService] ${message}`, data || '');
    }
  }

  /**
   * Tạo thread chat mới
   * Endpoint: POST /v1/user/chat/threads
   */
  async createThread(name?: string): Promise<CreateThreadResponse> {
    const threadName = name || `New Chat ${Date.now()}`;

    this.log('Creating thread', { name: threadName });

    const requestBody: CreateThreadRequest = {
      name: threadName
    };

    try {
      const response = await apiClient.post<CreateThreadResponse>(
        '/user/chat/threads',
        requestBody,
        {
          timeout: this.timeout
        }
      );

      this.log('Thread created successfully', response.result);

      // apiClient trả về ApiResponseDto<CreateThreadResponse>
      // response.result chính là CreateThreadResponse
      return response.result;
    } catch (error) {
      this.log('Failed to create thread', error);
      throw this.handleApiError(error, 'Failed to create thread');
    }
  }

  /**
   * Lấy danh sách threads
   * Endpoint: GET /v1/user/chat/threads
   */
  async getThreads(query?: GetThreadsQuery): Promise<GetThreadsResponse> {
    console.log('[ChatApiService] Getting threads API call', { query, timestamp: Date.now() });
    this.log('Getting threads', query);

    // Tạo query parameters
    const params = new URLSearchParams();
    if (query?.page) params.append('page', query.page.toString());
    if (query?.limit) params.append('limit', query.limit.toString());
    if (query?.sortBy) params.append('sortBy', query.sortBy);
    if (query?.sortDirection) params.append('sortDirection', query.sortDirection);
    if (query?.search) params.append('search', query.search);

    const url = `/user/chat/threads${params.toString() ? `?${params.toString()}` : ''}`;

    try {
      const response = await apiClient.get<GetThreadsResponse>(url, {
        timeout: this.timeout
      });

      this.log('Threads retrieved successfully', response.result);

      // apiClient trả về ApiResponseDto<GetThreadsResponse>
      // response.result chính là GetThreadsResponse
      return response.result;
    } catch (error) {
      this.log('Failed to get threads', error);
      throw this.handleApiError(error, 'Failed to get threads');
    }
  }

  /**
   * Lấy lịch sử tin nhắn trong thread
   * Endpoint: GET /v1/user/chat/threads/{threadId}/messages
   */
  async getMessages(
    threadId: string,
    query?: GetMessagesQuery
  ): Promise<GetMessagesResponseData> {
    console.log('[ChatApiService] Getting messages API call', { threadId, query, timestamp: Date.now() });
    this.log('Getting messages', { threadId, query });

    // Validate threadId
    if (!threadId || threadId.trim() === '') {
      throw new Error('Thread ID is required');
    }

    // Tạo query parameters với default values
    const params = new URLSearchParams();
    if (query?.page) params.append('page', query.page.toString());
    if (query?.limit) params.append('limit', query.limit.toString());
    if (query?.sortBy) params.append('sortBy', query.sortBy);
    if (query?.sortDirection) params.append('sortDirection', query.sortDirection);
    if (query?.role) params.append('role', query.role);

    const url = `/user/chat/threads/${threadId}/messages${params.toString() ? `?${params.toString()}` : ''}`;

    try {
      const response = await apiClient.get<GetMessagesResponseData>(url, {
        timeout: this.timeout
      });

      // apiClient trả về ApiResponseDto<GetMessagesResponse>
      // response.result chính là GetMessagesResponse
      return response.result;
    } catch (error) {
      this.log('Failed to get messages', { threadId, error });
      throw this.handleApiError(error, 'Failed to get messages');
    }
  }

  /**
   * Gửi tin nhắn chat (legacy method)
   * Endpoint: POST /v1/chat/message
   */
  async sendMessage(
    content: string,
    threadId: string,
    alwaysApproveToolCall: boolean = false
  ): Promise<SendMessageResponse> {
    this.log('Sending message', { content, threadId, alwaysApproveToolCall });

    // Tạo content blocks từ string
    const contentBlocks: ContentBlock[] = [
      {
        type: 'text',
        content: content
      }
    ];

    // Tạo request body theo format yêu cầu
    const requestBody: SendMessageRequest = {
      contentBlocks,
      threadId,
      alwaysApproveToolCall
    };

    try {
      const response = await apiClient.post<SendMessageResponse>(
        '/chat/message',
        requestBody,
        {
          timeout: this.timeout
        }
      );

      this.log('Message sent successfully', response.result);

      // apiClient trả về ApiResponseDto<SendMessageResponse>
      // response.result chính là SendMessageResponse
      return response.result;
    } catch (error) {
      this.log('Failed to send message', error);
      throw this.handleApiError(error, 'Failed to send message');
    }
  }

  /**
   * Gửi tin nhắn với DTO mới (hỗ trợ reply, modify, attachments)
   * Endpoint: POST /v1/chat/message
   */
  async sendMessageWithDto(
    messageRequest: any
  ): Promise<any> {
    this.log('Sending message with DTO', messageRequest);

    try {
      const response = await apiClient.post<any>(
        '/chat/message',
        messageRequest,
        {
          timeout: this.timeout
        }
      );

      this.log('Message sent successfully with DTO', response.result);

      // apiClient trả về ApiResponseDto<MessageResponseDto>
      // response.result chính là MessageResponseDto
      return response.result;
    } catch (error) {
      this.log('Failed to send message with DTO', error);
      throw this.handleApiError(error, 'Failed to send message');
    }
  }

  /**
   * Dừng chat run
   * Endpoint: DELETE /v1/chat/runs/{runId}
   */
  async stopRun(runId: string): Promise<StopRunResponse> {
    this.log('Stopping run', { runId });

    try {
      const response = await apiClient.delete<StopRunResponse>(
        `/chat/runs/${runId}`,
        {
          timeout: this.timeout
        }
      );

      this.log('Run stopped successfully', response.result);
      
      // apiClient trả về ApiResponseDto<StopRunResponse>
      // response.result chính là StopRunResponse
      return response.result;
    } catch (error) {
      this.log('Failed to stop run', error);
      throw this.handleApiError(error, 'Failed to stop run');
    }
  }

  /**
   * Xử lý lỗi API và tạo error message phù hợp
   */
  private handleApiError(error: unknown, defaultMessage: string): Error {
    if (error && typeof error === 'object' && 'response' in error) {
      const apiError = error as { response?: { data?: { message?: string } } };
      if (apiError.response?.data?.message) {
        return new Error(apiError.response.data.message);
      }
    }

    if (error instanceof Error && error.message) {
      return new Error(error.message);
    }

    return new Error(defaultMessage);
  }

  /**
   * Cập nhật base URL
   */
  updateBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
    this.log('Base URL updated', { baseUrl });
  }

  /**
   * Cập nhật timeout
   */
  updateTimeout(timeout: number): void {
    this.timeout = timeout;
    this.log('Timeout updated', { timeout });
  }

  /**
   * Cập nhật debug mode
   */
  updateDebug(debug: boolean): void {
    this.debug = debug;
    this.log('Debug mode updated', { debug });
  }

  /**
   * Lấy thông tin cấu hình hiện tại
   */
  getConfig(): { baseUrl: string; timeout: number; debug: boolean } {
    return {
      baseUrl: this.baseUrl,
      timeout: this.timeout,
      debug: this.debug
    };
  }
}
