/**
 * ToolCallApprovalForm Component
 * Form để approve/reject tool calls khi có tool_call_interrupt event
 */

import React from 'react';
import { ToolCallInterruptData } from '@/shared/services/chat-sse.service';

export interface ToolCallApprovalFormProps {
  /**
   * Tool call data từ interrupt event
   */
  toolCallData: ToolCallInterruptData;

  /**
   * Callback khi user chọn decision
   */
  onApprove: (decision: 'yes' | 'no' | 'always') => void;

  /**
   * Callback khi user dismiss form
   */
  onDismiss: () => void;

  /**
   * Loading state khi đang gửi approval
   */
  isLoading?: boolean;
}

/**
 * ToolCallApprovalForm Component
 */
const ToolCallApprovalForm: React.FC<ToolCallApprovalFormProps> = ({
  toolCallData,
  onApprove,
  onDismiss,
  isLoading = false
}) => {
  const handleDecision = (decision: 'yes' | 'no' | 'always') => {
    onApprove(decision);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              🔧 Tool Call Approval
            </h3>
            <button
              onClick={onDismiss}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4">
          <div className="space-y-4">
            {/* Tool Info */}
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                Tool: {toolCallData.toolName}
              </h4>
              {toolCallData.toolDescription && (
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                  {toolCallData.toolDescription}
                </p>
              )}
              
              {/* Parameters */}
              {toolCallData.parameters && Object.keys(toolCallData.parameters).length > 0 && (
                <div>
                  <p className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Parameters:
                  </p>
                  <div className="bg-white dark:bg-gray-800 rounded border p-3">
                    <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                      {JSON.stringify(toolCallData.parameters, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>

            {/* Question */}
            <div className="text-center">
              <p className="text-gray-700 dark:text-gray-300">
                Do you want to allow this tool to be executed?
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-col space-y-3">
            {/* Primary Actions */}
            <div className="flex space-x-3">
              <button
                onClick={() => handleDecision('yes')}
                disabled={isLoading}
                className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Approving...
                  </div>
                ) : (
                  '✅ Yes, Allow'
                )}
              </button>
              
              <button
                onClick={() => handleDecision('no')}
                disabled={isLoading}
                className="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                ❌ No, Deny
              </button>
            </div>

            {/* Always Allow */}
            <button
              onClick={() => handleDecision('always')}
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              🔄 Always Allow (Auto-approve future calls)
            </button>

            {/* Cancel */}
            <button
              onClick={onDismiss}
              disabled={isLoading}
              className="w-full bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>

        {/* Footer Info */}
        <div className="px-6 py-3 bg-gray-50 dark:bg-gray-900 rounded-b-lg">
          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Role: {toolCallData.role} • Thread: {toolCallData.threadId.substring(0, 8)}...</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolCallApprovalForm;
