/**
 * ToolCallApprovalForm Component
 * Inline message để approve/reject tool calls khi có tool_call_interrupt event
 */

import React from 'react';
import { ToolCallInterruptData } from '@/shared/services/chat-sse.service';

export interface ToolCallApprovalFormProps {
  /**
   * Tool call data từ interrupt event
   */
  toolCallData: ToolCallInterruptData;

  /**
   * Callback khi user chọn decision
   */
  onApprove: (decision: 'yes' | 'no' | 'always') => void;

  /**
   * Callback khi user dismiss form
   */
  onDismiss: () => void;

  /**
   * Loading state khi đang gửi approval
   */
  isLoading?: boolean;
}

/**
 * ToolCallApprovalForm Component
 */
const ToolCallApprovalForm: React.FC<ToolCallApprovalFormProps> = ({
  toolCallData,
  onApprove,
  onDismiss,
  isLoading = false
}) => {
  const handleDecision = (decision: 'yes' | 'no' | 'always') => {
    onApprove(decision);
  };

  return (
    <div className="mx-4 my-2">
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
              <span className="text-blue-600 dark:text-blue-300 text-sm">🔧</span>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                Tool Call Approval Required
              </h3>
              <p className="text-xs text-blue-700 dark:text-blue-300">
                AI wants to use: <span className="font-medium">{toolCallData.toolName}</span>
              </p>
            </div>
          </div>
          <button
            onClick={onDismiss}
            disabled={isLoading}
            className="text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 disabled:opacity-50 p-1"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="space-y-3">
          {/* Tool Description */}
          {toolCallData.toolDescription && (
            <p className="text-sm text-blue-700 dark:text-blue-300">
              {toolCallData.toolDescription}
            </p>
          )}

          {/* Parameters */}
          {toolCallData.parameters && Object.keys(toolCallData.parameters).length > 0 && (
            <div>
              <p className="text-xs font-medium text-blue-900 dark:text-blue-100 mb-2">
                Parameters:
              </p>
              <div className="bg-white dark:bg-gray-800 rounded border border-blue-200 dark:border-blue-700 p-2">
                <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap overflow-x-auto">
                  {JSON.stringify(toolCallData.parameters, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Question */}
          <p className="text-sm text-blue-800 dark:text-blue-200 font-medium">
            Do you want to allow this tool to be executed?
          </p>
        </div>

        {/* Actions */}
        <div className="mt-4 pt-3 border-t border-blue-200 dark:border-blue-700">
          <div className="flex flex-wrap gap-2">
            {/* Primary Actions */}
            <button
              onClick={() => handleDecision('yes')}
              disabled={isLoading}
              className="flex-1 min-w-0 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-medium py-2 px-3 rounded text-sm transition-colors"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin mr-1"></div>
                  <span className="text-xs">Approving...</span>
                </div>
              ) : (
                '✅ Allow'
              )}
            </button>

            <button
              onClick={() => handleDecision('no')}
              disabled={isLoading}
              className="flex-1 min-w-0 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white font-medium py-2 px-3 rounded text-sm transition-colors"
            >
              ❌ Deny
            </button>

            {/* Always Allow */}
            <button
              onClick={() => handleDecision('always')}
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-3 rounded text-sm transition-colors mt-2"
            >
              🔄 Always Allow
            </button>
          </div>
        </div>

        {/* Footer Info */}
        <div className="mt-3 pt-2 border-t border-blue-200 dark:border-blue-700">
          <div className="flex items-center text-xs text-blue-600 dark:text-blue-400">
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Role: {toolCallData.role} • Thread: {toolCallData.threadId.substring(0, 8)}...</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolCallApprovalForm;
