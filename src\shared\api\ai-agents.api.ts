import { apiClient } from './axios';
import { AgentSimpleListResult, AgentSimpleQueryParams } from '@/shared/types/ai-agents.types';
import {
  SendMessageRequest,
  SendMessageResponse,
  StopRunResponse
} from '@/shared/types/chat-streaming.types';
import { MessageRequestDto } from '@/shared/services/dto/message-request.dto';
import { MessageResponseDto } from '@/shared/services/dto/message-response.dto';

/**
 * API service cho AI Agents
 */
export class AIAgentsApi {
  /**
   * Lấy danh sách agents đơn giản
   */
  static async getSimpleAgents(params: AgentSimpleQueryParams = {}): Promise<AgentSimpleListResult> {
    // Set default values
    const defaultParams = {
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortDirection: 'DESC' as const,
      ...params
    };

    const response = await apiClient.get<AgentSimpleListResult>(
      '/user/agents/simple',
      { params: defaultParams }
    );

    return response.result;
  }

  /**
   * <PERSON><PERSON><PERSON> tin nhắn chat - API mới
   * Endpoint: POST /v1/chat/message
   */
  static async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    const response = await apiClient.post<SendMessageResponse>(
      '/chat/message',
      request
    );

    // apiClient đã trả về ApiResponseDto<SendMessageResponse>
    // SendMessageResponse đã có structure { code, message, result }
    // Nên ta trả về response.result (là SendMessageResponse)
    return response.result;
  }

  /**
   * Dừng chat run - API mới
   * Endpoint: DELETE /v1/chat/runs/{runId}
   */
  static async stopRun(runId: string): Promise<StopRunResponse> {
    const response = await apiClient.delete<StopRunResponse>(
      `/chat/runs/${runId}`
    );

    // Tương tự với StopRunResponse
    return response.result;
  }


}
