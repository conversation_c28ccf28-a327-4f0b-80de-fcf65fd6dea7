import { apiClient } from './axios';
import { AgentSimpleListResult, AgentSimpleQueryParams } from '@/shared/types/ai-agents.types';
import {
  SendMessageRequest,
  SendMessageResponse,
  MessageRequest,
  MessageResponse,
  MessageContentBlock,
  MessageAttachmentContext,
  StopRunResponse
} from '@/shared/types/chat-streaming.types';

/**
 * API service cho AI Agents
 */
export class AIAgentsApi {
  /**
   * Lấy danh sách agents đơn giản
   */
  static async getSimpleAgents(params: AgentSimpleQueryParams = {}): Promise<AgentSimpleListResult> {
    // Set default values
    const defaultParams = {
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortDirection: 'DESC' as const,
      ...params
    };

    const response = await apiClient.get<AgentSimpleListResult>(
      '/user/agents/simple',
      { params: defaultParams }
    );

    return response.result;
  }

  /**
   * <PERSON><PERSON><PERSON> tin nhắn chat - hỗ trợ cả legacy và message mới
   * Endpoint: POST /v1/chat/message
   *
   * @param request - SendMessageRequest (legacy) hoặc MessageRequest (mới với reply, modify, attachments)
   * @returns SendMessageResponse (legacy) hoặc MessageResponse (mới với modification details)
   */
  static async sendMessage(request: SendMessageRequest | MessageRequest): Promise<SendMessageResponse | MessageResponse> {
    const response = await apiClient.post<SendMessageResponse | MessageResponse>(
      '/chat/message',
      request
    );

    // apiClient trả về ApiResponseDto<T>
    // response.result chính là T (SendMessageResponse hoặc MessageResponse)
    return response.result;
  }

  /**
   * Gửi tin nhắn text đơn giản (helper method)
   * @param content - Nội dung tin nhắn
   * @param threadId - ID của thread
   * @param alwaysApproveToolCall - Tự động approve tool calls
   */
  static async sendTextMessage(
    content: string,
    threadId: string,
    alwaysApproveToolCall: boolean = false
  ): Promise<SendMessageResponse> {
    const request: SendMessageRequest = {
      contentBlocks: [{ type: 'text', content }],
      threadId,
      alwaysApproveToolCall
    };

    return this.sendMessage(request) as Promise<SendMessageResponse>;
  }

  /**
   * Reply tin nhắn (helper method)
   * @param content - Nội dung reply
   * @param threadId - ID của thread
   * @param replyToMessageId - ID của message được reply
   */
  static async replyToMessage(
    content: string,
    threadId: string,
    replyToMessageId: string
  ): Promise<MessageResponse> {
    const request: MessageRequest = {
      contentBlocks: [{ type: 'text', content }],
      threadId,
      replyToMessageId
    };

    return this.sendMessage(request) as Promise<MessageResponse>;
  }

  /**
   * Modify tin nhắn hiện có (helper method)
   * @param content - Nội dung mới
   * @param threadId - ID của thread
   * @param messageId - ID của message cần modify
   */
  static async modifyMessage(
    content: string,
    threadId: string,
    messageId: string
  ): Promise<MessageResponse> {
    const request: MessageRequest = {
      contentBlocks: [{ type: 'text', content }],
      threadId,
      messageId
    };

    return this.sendMessage(request) as Promise<MessageResponse>;
  }

  /**
   * Gửi tin nhắn với attachments (helper method)
   * @param content - Nội dung tin nhắn
   * @param threadId - ID của thread
   * @param attachments - Danh sách attachments
   */
  static async sendMessageWithAttachments(
    content: string,
    threadId: string,
    attachments: { type: 'file' | 'image'; id: string }[]
  ): Promise<MessageResponse> {
    const contentBlocks: MessageContentBlock[] = [
      { type: 'text', content }
    ];

    const attachmentContext: MessageAttachmentContext[] = [];

    // Thêm attachment blocks và context
    attachments.forEach(attachment => {
      if (attachment.type === 'file') {
        contentBlocks.push({ type: 'file', fileId: attachment.id });
        attachmentContext.push({ type: 'file', fileId: attachment.id });
      } else if (attachment.type === 'image') {
        contentBlocks.push({ type: 'image', imageId: attachment.id });
        attachmentContext.push({ type: 'image', imageId: attachment.id });
      }
    });

    const request: MessageRequest = {
      contentBlocks,
      threadId,
      attachmentContext
    };

    return this.sendMessage(request) as Promise<MessageResponse>;
  }

  /**
   * Dừng chat run - API mới
   * Endpoint: DELETE /v1/chat/runs/{runId}
   */
  static async stopRun(runId: string): Promise<StopRunResponse> {
    const response = await apiClient.delete<StopRunResponse>(
      `/chat/runs/${runId}`
    );

    // Tương tự với StopRunResponse
    return response.result;
  }


}
