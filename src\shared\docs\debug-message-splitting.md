# Debug Setup - Message Splitting Analysis

Tài liệu này mô tả debug setup để phân tích nguyên nhân message bị split.

## 🔍 **Nguyên nhân có thể gây Message Splitting**

### **1. Role Change Logic**
- Logic `currentStreamingMessageRef.current.sender !== mappedSender` 
- Nếu `mapRoleToMessageSender` trả về sender khác nhau cho cùng AI response
- VD: `supervisor` → `supervisor` vs `assistant` → `ai`

### **2. Missing Metadata**
- Message đầu tiên thiếu `originalRole` và `streamingRole`
- Gây inconsistency trong tracking

### **3. Hardcoded Sender**
- Hardcode `sender: 'ai'` trong `onLLMStreamEnd` và `onMessageCreated`
- Có thể gây conflict với sender từ `mapRoleToMessageSender`

## ✅ **Debug Logs đã thêm**

### **1. Message Creation Tracking**
```typescript
// Khi tạo message mới
console.log('[useChatStream] 🆕 CREATING NEW MESSAGE:', {
  role,
  mappedSender,
  text: text.substring(0, 50) + '...'
});

console.log('[useChatStream] 📝 Adding new message to list. Current count:', prev.length);
console.log('[useChatStream] ✅ New message created with ID:', newMessage.id);
```

### **2. Role Change Detection**
```typescript
// Khi detect role change
console.warn('[useChatStream] 🔄 ROLE CHANGE DETECTED - THIS IS THE PROBLEM!:', {
  currentSender: currentStreamingMessageRef.current.sender,
  newSender: mappedSender,
  currentRole: currentRole,
  newRole: role,
  text: text.substring(0, 50) + '...',
  currentMessageId: currentStreamingMessageRef.current.id,
  currentMessageContent: currentStreamingMessageRef.current.content?.toString().substring(0, 50) + '...'
});
```

### **3. Message Continuation Tracking**
```typescript
// Khi continue message hiện tại
console.log('[useChatStream] ➕ CONTINUING EXISTING MESSAGE:', {
  messageId: currentStreamingMessageRef.current.id,
  currentLength: currentStreamingMessageRef.current.content?.toString().length || 0,
  newText: text.substring(0, 20) + '...',
  role,
  mappedSender
});
```

## 🎯 **Expected Debug Flow**

### **Normal Flow (1 Message)**
```
1. 🆕 CREATING NEW MESSAGE: {role: "supervisor", mappedSender: "ai"}
2. ➕ CONTINUING EXISTING MESSAGE: {role: "supervisor", mappedSender: "ai"} (multiple times)
3. 🏁 LLM Stream End for role: supervisor
4. 🆔 MESSAGE CREATED event: {role: "assistant"}
5. ✅ MessageId assigned and message finalized
```

### **Problem Flow (2 Messages)**
```
1. 🆕 CREATING NEW MESSAGE: {role: "supervisor", mappedSender: "ai"}
2. ➕ CONTINUING EXISTING MESSAGE: {role: "supervisor", mappedSender: "ai"} (some times)
3. 🔄 ROLE CHANGE DETECTED - THIS IS THE PROBLEM!: {currentSender: "ai", newSender: "supervisor"}
4. 🆕 CREATING NEW MESSAGE: {role: "supervisor", mappedSender: "supervisor"} ← PROBLEM!
5. Two separate messages created
```

## 🔧 **Debugging Steps**

### **Step 1: Check Role Mapping**
```javascript
// Test mapRoleToMessageSender function
console.log('supervisor →', mapRoleToMessageSender('supervisor')); // Should be 'ai'
console.log('assistant →', mapRoleToMessageSender('assistant')); // Should be 'ai'
```

### **Step 2: Monitor Console Logs**
1. Open browser DevTools → Console
2. Send a message
3. Look for these key logs:
   - `🆕 CREATING NEW MESSAGE` - Should only appear once
   - `🔄 ROLE CHANGE DETECTED` - Should NOT appear for same AI response
   - `➕ CONTINUING EXISTING MESSAGE` - Should appear multiple times

### **Step 3: Check Message Count**
```javascript
// Check how many AI messages are created
const aiMessages = messages.filter(m => m.sender === 'ai');
console.log('AI messages count:', aiMessages.length); // Should be 1 for single response
```

### **Step 4: Analyze Role Change**
If you see `🔄 ROLE CHANGE DETECTED`, check:
- `currentSender` vs `newSender` - Should be same for AI response
- `currentRole` vs `newRole` - May be different but sender should be same
- `mappedSender` consistency

## 🚨 **Common Issues**

### **Issue 1: mapRoleToMessageSender Inconsistency**
```typescript
// ❌ Problem: Different senders for AI roles
mapRoleToMessageSender('supervisor') → 'supervisor'
mapRoleToMessageSender('assistant') → 'ai'

// ✅ Solution: Unified AI mapping
mapRoleToMessageSender('supervisor') → 'ai'
mapRoleToMessageSender('assistant') → 'ai'
```

### **Issue 2: Hardcoded Sender Conflict**
```typescript
// ❌ Problem: Hardcoded sender overrides mapping
sender: 'ai' // in onLLMStreamEnd/onMessageCreated

// ✅ Solution: Use consistent mapping
sender: mappedSender // from mapRoleToMessageSender
```

### **Issue 3: Missing Metadata**
```typescript
// ❌ Problem: Missing role tracking
metadata: {
  agentId: currentAgentId
}

// ✅ Solution: Complete role tracking
metadata: {
  agentId: currentAgentId,
  originalRole: role,
  streamingRole: role
}
```

## 🎯 **Expected Results**

### **Single Message Flow**
- Only one `🆕 CREATING NEW MESSAGE` log
- Multiple `➕ CONTINUING EXISTING MESSAGE` logs
- No `🔄 ROLE CHANGE DETECTED` logs
- Final message count = 1

### **Debug Output Example**
```
[useChatStream] 🆕 CREATING NEW MESSAGE: {role: "supervisor", mappedSender: "ai", text: "Đúng vậy! Tôi ở đây để hỗ trợ bạn với bất kỳ câu..."}
[useChatStream] 📝 Adding new message to list. Current count: 2
[useChatStream] ✅ New message created with ID: abc-123
[useChatStream] ➕ CONTINUING EXISTING MESSAGE: {messageId: "abc-123", currentLength: 1, newText: "úng...", role: "supervisor", mappedSender: "ai"}
[useChatStream] ➕ CONTINUING EXISTING MESSAGE: {messageId: "abc-123", currentLength: 4, newText: " vậy...", role: "supervisor", mappedSender: "ai"}
... (multiple continue logs)
[useChatStream] 🏁 LLM Stream End for role: supervisor Current message: abc-123
[useChatStream] ✅ Message marked as completed for role: supervisor - waiting for messageId
[useChatStream] 🆔 MESSAGE CREATED event: {messageId: "fbf627e2-7225-4fd0-af27-83aaa5459388", role: "assistant"}
[useChatStream] ✅ MessageId assigned and message finalized: fbf627e2-7225-4fd0-af27-83aaa5459388
```

## 🔍 **Next Steps**

1. **Test với debug logs** - Chạy lại và check console
2. **Identify root cause** - Tìm log `🔄 ROLE CHANGE DETECTED`
3. **Fix mapping issue** - Sửa `mapRoleToMessageSender` nếu cần
4. **Remove hardcoded senders** - Đảm bảo consistency
5. **Verify single message** - Confirm chỉ có 1 message được tạo

Với debug setup này, chúng ta sẽ tìm ra chính xác nguyên nhân gây message splitting!
