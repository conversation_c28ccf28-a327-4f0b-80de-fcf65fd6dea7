/**
 * useChatStream Hook
 * Hook chính để quản lý chat streaming với luồng mới
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { flushSync } from 'react-dom';
import { ChatApiService } from '@/shared/services/chat-api.service';
import { ChatSSEService, ToolCallInterruptData } from '@/shared/services/chat-sse.service';
import { chatConfigService } from '@/shared/services/chat-config.service';
import {
  ChatMessage,
  MessageStatus,
  ChatMessageRequest,
  ChatMessageResponse,
  GetMessagesQuery,
  convertThreadMessagesToChatMessages,
  GetMessagesResponseData,
  mapRoleToMessageSender,
} from '@/shared/types/chat-streaming.types';
import { v4 as uuidv4 } from 'uuid';
import { chatApiSingleton } from '@/shared/utils/chatApiSingleton';
import { ThreadsService } from '@/modules/threads/services';
import { isThreadDeleted } from '@/modules/threads/services/deleted-thread-tracker.service';
import { toolApprovalSettingsService } from '@/shared/services/tool-approval-settings.service';

/**
 * Thread event callbacks
 */
export interface ThreadEventCallbacks {
  onThreadCreated?: (threadId: string, threadName: string) => void;
  onThreadLoaded?: (threadId: string, threadName: string) => void;
  onThreadSwitched?: (fromThreadId: string, toThreadId: string) => void;
  onThreadNameChanged?: (threadId: string, newName: string) => void;
  onThreadDeleted?: (threadId: string) => void;
}

/**
 * Configuration cho useChatStream hook
 */
export interface UseChatStreamConfig {
  agentId?: string;
  apiBaseUrl?: string;
  sseBaseUrl?: string;
  alwaysApproveToolCall?: boolean;
  getAuthToken: () => string | Promise<string>;
  debug?: boolean;

  /**
   * Configuration cho message history
   */
  messageHistory?: {
    pageSize?: number;
    autoLoad?: boolean;
    timeout?: number;
  };

  /**
   * Thread event callbacks
   */
  threadEvents?: ThreadEventCallbacks;

  /**
   * RPoint update callback
   */
  onRPointUpdate?: (rPointCost: number, updatedBalance: string, timestamp: number) => void;
}

/**
 * Return type cho useChatStream hook
 */
export interface UseChatStreamReturn {
  // State
  messages: ChatMessage[];
  isStreaming: boolean;
  isLoading: boolean;
  isThinking: boolean;
  currentStreamingText: string;
  currentRunId: string | null;
  threadId: string | null;
  threadName: string | null;
  isCreatingThread: boolean;
  isLoadingThreads: boolean;

  // Worker thinking state
  workerThinking: {
    isVisible: boolean;
    content: string;
    isStreaming: boolean;
  };

  // Tool call interrupt state
  toolCallInterrupt: ToolCallInterruptData | null;
  approveToolCall: (decision: 'yes' | 'no' | 'always') => Promise<void>;
  dismissToolCallInterrupt: () => void;

  // Message History
  historyMessages: ChatMessage[];
  isLoadingHistory: boolean;
  isLoadingMoreHistory: boolean;
  hasMoreHistory: boolean;
  historyError: string | null;
  totalHistoryItems: number;

  // Actions
  sendMessage: (contentOrRequest: string | ChatMessageRequest, threadId?: string, alwaysApproveToolCall?: boolean) => Promise<ChatMessageResponse | null>;
  stopStreaming: () => Promise<void>;
  clearMessages: () => void;
  createNewThread: (name?: string) => Promise<{ threadId: string; threadName: string }>;
  loadLatestThread: () => Promise<void>;
  loadSpecificThread: (threadId: string) => Promise<void>;
  switchToThread: (threadId: string) => Promise<void>;
  getCurrentThreadId: () => string | null;
  updateThreadName: (threadId: string, newName: string) => Promise<void>;
  retryLastMessage: () => Promise<void>;

  // Message History Actions
  loadMoreHistory: () => Promise<void>;
  refreshHistory: () => Promise<void>;

  // Status
  isConnected: boolean;
  error: string | null;
  streamError: {
    message: string;
    details?: unknown;
    retryContent?: string;
  } | null;
}

/**
 * useChatStream Hook
 */
export function useChatStream(config: UseChatStreamConfig): UseChatStreamReturn {
  // State
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStreamingText, setCurrentStreamingText] = useState('');
  const [currentRunId, setCurrentRunId] = useState<string | null>(null);
  const [threadId, setThreadId] = useState<string | null>(null);
  const [threadName, setThreadName] = useState<string | null>(null);
  const [isCreatingThread, setIsCreatingThread] = useState(false);
  const [isLoadingThreads, setIsLoadingThreads] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentRole, setCurrentRole] = useState<string>('ai'); // Track current role từ SSE
  const [isThinking, setIsThinking] = useState(false); // Track thinking state từ tool_call_start/end

  // Worker thinking state
  const [workerThinking, setWorkerThinking] = useState({
    isVisible: false,
    content: '',
    isStreaming: false
  });

  // Current agent ID for message metadata
  const [currentAgentId, setCurrentAgentId] = useState<string | null>(null);

  // Tool call interrupt state
  const [toolCallInterrupt, setToolCallInterrupt] = useState<ToolCallInterruptData | null>(null);

  // Stream error state
  const [streamError, setStreamError] = useState<{
    message: string;
    details?: unknown;
    retryContent?: string;
  } | null>(null);

  // Message History State
  const [historyMessages, setHistoryMessages] = useState<ChatMessage[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [isLoadingMoreHistory, setIsLoadingMoreHistory] = useState(false);
  const [hasMoreHistory, setHasMoreHistory] = useState(true);
  const [historyError, setHistoryError] = useState<string | null>(null);
  const [currentHistoryPage, setCurrentHistoryPage] = useState(1);
  const [totalHistoryItems, setTotalHistoryItems] = useState(0);

  // Services refs
  const apiServiceRef = useRef<ChatApiService | null>(null);
  const sseServiceRef = useRef<ChatSSEService | null>(null);
  const currentStreamingMessageRef = useRef<ChatMessage | null>(null);
  const hasLoadedInitialThreadRef = useRef<boolean>(false);
  const lastMessageContentRef = useRef<string>(''); // Lưu content để retry

  // Debug refs for token tracking
  const tokenCountRef = useRef<number>(0);
  const lastTokenTimeRef = useRef<number>(0);

  // Message History refs
  const currentThreadIdRef = useRef<string | null>(null);
  const isHistoryLoadingRef = useRef(false);
  const historyTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function refs to avoid circular dependencies
  const stopStreamingRef = useRef<(() => Promise<void>) | null>(null);

  // Initialize services
  useEffect(() => {
    const chatConfig = chatConfigService.getConfig();
    const finalConfig = {
      agentId: config.agentId || chatConfig.agentId,
      apiBaseUrl: config.apiBaseUrl || chatConfig.apiBaseUrl,
      sseBaseUrl: config.sseBaseUrl || chatConfig.sseBaseUrl,
      alwaysApproveToolCall: config.alwaysApproveToolCall ?? chatConfig.alwaysApproveToolCall,
      debug: config.debug ?? chatConfig.debug,
      getAuthToken: config.getAuthToken
    };

    // ✅ Update current agent ID
    setCurrentAgentId(finalConfig.agentId);

    // Initialize API service
    apiServiceRef.current = new ChatApiService(
      finalConfig.apiBaseUrl,
      30000, // timeout
      finalConfig.debug
    );

    // Initialize SSE service với debug mode và auth token
    sseServiceRef.current = new ChatSSEService(
      finalConfig.sseBaseUrl,
      true, // Enable debug mode
      finalConfig.getAuthToken // Pass auth token getter
    );

    // Setup SSE callbacks
    sseServiceRef.current.setCallbacks({
      onConnected: (data) => {
        console.log('[useChatStream] SSE Connected:', data);
        setIsConnected(true);
        setError(null);
      },

      onToolCallStart: (role: string) => {
        console.log('[useChatStream] 🤔 Tool call started for role:', role);
        setIsThinking(true);
      },

      onToolCallEnd: (role: string) => {
        console.log('[useChatStream] ✅ Tool call ended for role:', role);
        setIsThinking(false);
      },

      onToolCallInterrupt: (toolCallData) => {
        console.log('[useChatStream] ⚠️ Tool call interrupt:', toolCallData);
        // Set tool call interrupt state để hiển thị approval form
        setToolCallInterrupt(toolCallData);
      },
      
      onTextToken: (text: string, role: string) => {
        console.log('[useChatStream] 🔤 TEXT TOKEN RECEIVED:', {
          text,
          role,
          isStreaming,
          isLoading,
          currentStreamingMessage: !!currentStreamingMessageRef.current
        });

        // ✅ CRITICAL CHECK: Ignore tokens if not in streaming/loading state
        if (!isStreaming && !isLoading) {
          console.warn('[useChatStream] ⚠️ IGNORING TOKEN - not in streaming/loading state:', {
            text,
            role,
            isStreaming,
            isLoading
          });
          return;
        }

        // Track token frequency
        const now = Date.now();
        tokenCountRef.current += 1;
        lastTokenTimeRef.current = now;

        // Map role từ SSE event thành MessageSender
        const mappedSender = mapRoleToMessageSender(role);

        // Nếu đây là token đầu tiên, bật streaming
        if (!isStreaming) {
          setIsStreaming(true);
        }

        // ✅ Xử lý worker messages riêng - hiển thị trong thinking box
        if (role === 'worker') {
          setWorkerThinking(prev => ({
            isVisible: true,
            content: prev.content + text,
            isStreaming: true
          }));

          // Không tạo message cho worker, chỉ update thinking box
          return;
        }

        // Tạo message mới nếu chưa có hoặc role thay đổi
        if (!currentStreamingMessageRef.current) {
          // Tạo message mới
          console.log('[useChatStream] 🆕 CREATING NEW MESSAGE:', {
            role,
            mappedSender,
            text: text.substring(0, 50) + '...'
          });

          const newMessage: ChatMessage = {
            id: uuidv4(),
            content: text,
            sender: mappedSender,
            timestamp: new Date(),
            status: MessageStatus.STREAMING,
            threadId: threadId!,
            metadata: {
              ...(currentAgentId && { agentId: currentAgentId }), // ✅ Lưu agentId vào metadata
              originalRole: role as any, // ✅ Lưu role gốc từ SSE
              streamingRole: role as any // ✅ Track role hiện tại đang stream
            }
          };

          // Force immediate UI update cho message mới
          flushSync(() => {
            setMessages(prev => {
              console.log('[useChatStream] 📝 Adding new message to list. Current count:', prev.length);
              return [...prev, newMessage];
            });
            setCurrentStreamingText(text);
          });

          currentStreamingMessageRef.current = newMessage;
          console.log('[useChatStream] ✅ New message created with ID:', newMessage.id);
        } else if (currentStreamingMessageRef.current.sender !== mappedSender) {
          // ⚠️ ROLE CHANGE DETECTED - Check if we should really split
          console.warn('[useChatStream] 🔄 ROLE CHANGE DETECTED - THIS IS THE PROBLEM!:', {
            currentSender: currentStreamingMessageRef.current.sender,
            newSender: mappedSender,
            currentRole: currentRole,
            newRole: role,
            text: text.substring(0, 50) + '...',
            currentMessageId: currentStreamingMessageRef.current.id,
            currentMessageContent: currentStreamingMessageRef.current.content?.toString().substring(0, 50) + '...'
          });

          // ✅ ONLY split if it's a significant role change (e.g., user -> ai, ai -> user)
          // Don't split for minor role variations within AI responses (assistant -> supervisor)
          const shouldSplitMessage = (
            (currentStreamingMessageRef.current.sender === 'user' && mappedSender === 'ai') ||
            (currentStreamingMessageRef.current.sender === 'ai' && mappedSender === 'user')
          );

          if (shouldSplitMessage) {
            console.log('[useChatStream] 🔄 Splitting message due to significant role change');

            // Finalize message hiện tại
            const finalizedMessage = {
              ...currentStreamingMessageRef.current,
              status: MessageStatus.COMPLETED
            };

            // Tạo message mới cho role mới
            const newMessage: ChatMessage = {
              id: uuidv4(),
              content: text,
              sender: mappedSender,
              timestamp: new Date(),
              status: MessageStatus.STREAMING,
              threadId: threadId!,
              metadata: {
                ...(currentAgentId && { agentId: currentAgentId }) // ✅ Lưu agentId vào metadata
              }
            };

            // Force immediate UI update
            flushSync(() => {
              setMessages(prev => [
                ...prev.map(msg => msg.id === currentStreamingMessageRef.current?.id ? finalizedMessage : msg),
                newMessage
              ]);
              setCurrentStreamingText(text);
            });

            currentStreamingMessageRef.current = newMessage;
          } else {
            console.log('[useChatStream] 🔄 Minor role change - continuing with same message');

            // Continue with same message, just append text
            currentStreamingMessageRef.current.content += text;

            // Update metadata với role mới nhất
            if (currentStreamingMessageRef.current.metadata) {
              (currentStreamingMessageRef.current.metadata as any).streamingRole = role;
            }

            // Force immediate UI update
            setMessages(prev => prev.map(msg =>
              msg.id === currentStreamingMessageRef.current?.id
                ? {
                    ...msg,
                    content: currentStreamingMessageRef.current!.content,
                    metadata: {
                      ...currentStreamingMessageRef.current!.metadata,
                      streamingRole: role as any
                    }
                  }
                : msg
            ));

            setCurrentStreamingText(
              typeof currentStreamingMessageRef.current.content === 'string'
                ? currentStreamingMessageRef.current.content
                : ''
            );
          }
        } else {
          // Simple direct update - append text immediately
          console.log('[useChatStream] ➕ CONTINUING EXISTING MESSAGE:', {
            messageId: currentStreamingMessageRef.current.id,
            currentLength: currentStreamingMessageRef.current.content?.toString().length || 0,
            newText: text.substring(0, 20) + '...',
            role,
            mappedSender
          });

          currentStreamingMessageRef.current.content += text;

          // Force immediate UI update
          setMessages(prev => prev.map(msg =>
            msg.id === currentStreamingMessageRef.current?.id
              ? {
                  ...msg,
                  content: currentStreamingMessageRef.current!.content
                }
              : msg
          ));

          setCurrentStreamingText(
            typeof currentStreamingMessageRef.current.content === 'string'
              ? currentStreamingMessageRef.current.content
              : ''
          );
        }

        // Update current role
        if (role !== currentRole) {
          setCurrentRole(role);
          console.log('[useChatStream] Role changed from', currentRole, 'to', role);
        }
      },
      
      onLLMStreamEnd: (role: string) => {
        // ✅ Check if we're still in valid streaming state
        if (!isStreaming && !isLoading) {
          console.warn('[useChatStream] ⚠️ Received LLM stream end but not in streaming/loading state, ignoring:', {
            role,
            isStreaming,
            isLoading
          });
          return;
        }

        // ✅ Xử lý worker stream end - chỉ update thinking box
        if (role === 'worker') {
          console.log('[useChatStream] Worker stream ended - stopping thinking box streaming');

          setWorkerThinking(prev => ({
            ...prev,
            isStreaming: false
          }));

          // Thinking box sẽ tự ẩn sau 2000ms
          return;
        }

        if (currentStreamingMessageRef.current) {
          console.log('[useChatStream] 🏁 LLM Stream End for role:', role, 'Current message:', currentStreamingMessageRef.current.id);

          if (role === 'supervisor' || role === 'assistant') {
            // ✅ IMPORTANT: Chỉ update status thành COMPLETED, KHÔNG finalize hoàn toàn
            // Để chờ message_created event gán messageId trước khi finalize
            const updatedMessage: ChatMessage = {
              ...currentStreamingMessageRef.current,
              status: MessageStatus.COMPLETED, // Mark as completed but keep in currentStreamingMessageRef
              metadata: {
                ...currentStreamingMessageRef.current.metadata,
                processingTime: Date.now() - currentStreamingMessageRef.current.timestamp.getTime(),
                llmStreamEndRole: role as any // Track role từ llm_stream_end
              }
            };

            setMessages(prev => prev.map(msg =>
              msg.id === currentStreamingMessageRef.current?.id
                ? updatedMessage
                : msg
            ));

            // ✅ Keep reference để message_created có thể assign messageId
            currentStreamingMessageRef.current = updatedMessage;
            console.log('[useChatStream] ✅ Message marked as completed for role:', role, '- waiting for messageId');
          }
        }
      },

      onMessageCreated: (messageId: string, role: string, contentPreview: string) => {
        console.log('[useChatStream] 🆔 MESSAGE CREATED event:', {
          messageId,
          role,
          contentPreview: contentPreview?.substring(0, 50) + '...',
          hasCurrentMessage: !!currentStreamingMessageRef.current,
          currentMessageId: currentStreamingMessageRef.current?.id,
          currentMessageStatus: currentStreamingMessageRef.current?.status
        });

        // ✅ Gán messageId cho message hiện tại (bất kể streaming state)
        // Vì message_created có thể đến sau khi streaming đã kết thúc
        if (currentStreamingMessageRef.current) {
          const finalMessage: ChatMessage = {
            ...currentStreamingMessageRef.current,
            messageId: messageId, // Gán messageId từ API
            status: MessageStatus.COMPLETED, // Ensure message is completed
            timestamp: new Date(), // Update final timestamp
            metadata: {
              ...currentStreamingMessageRef.current.metadata,
              apiMessageId: messageId,
              contentPreview: contentPreview,
              messageCreatedRole: role as any, // Track role từ message_created event
              finalizedAt: new Date().toISOString() as any // Track when message was finalized
            }
          };

          setMessages(prev => prev.map(msg =>
            msg.id === currentStreamingMessageRef.current?.id
              ? finalMessage
              : msg
          ));

          currentStreamingMessageRef.current = finalMessage;
          console.log('[useChatStream] ✅ MessageId assigned and message finalized:', messageId);
        } else {
          console.warn('[useChatStream] ⚠️ No current streaming message to assign messageId to');
        }
      },

      onStreamSessionEnd: () => {
        console.log('[useChatStream] 🏁 STREAM SESSION END - Cleaning up all streaming state');

        // ✅ KHÔNG finalize message ở đây nữa vì đã được finalize trong onLLMStreamEnd
        // Chỉ cleanup state
        console.log('[useChatStream] 🏁 Current message status:', {
          hasMessage: !!currentStreamingMessageRef.current,
          messageId: currentStreamingMessageRef.current?.id,
          status: currentStreamingMessageRef.current?.status,
          hasApiMessageId: !!currentStreamingMessageRef.current?.metadata?.apiMessageId
        });

        // Kết thúc toàn bộ session SSE
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentStreamingText(''); // ✅ Clear cursor
        setCurrentRunId(null);
        setIsConnected(false);
        currentStreamingMessageRef.current = null; // ✅ Clear reference sau khi đã finalize

        // ✅ Reset worker thinking state
        setWorkerThinking({
          isVisible: false,
          content: '',
          isStreaming: false
        });

        // Clear stream error khi session kết thúc thành công
        setStreamError(null);

        console.log('[useChatStream] ✅ Stream session cleanup completed');
      },

      onStreamEnd: () => {
        // Fallback cho trường hợp không có stream_session_end
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentStreamingText('');
        setCurrentRunId(null);
        setIsConnected(false);
        currentStreamingMessageRef.current = null;
      },

      onStreamError: (errorMessage: string, errorDetails?: unknown) => {
        // Set stream error state với retry content
        const errorState = {
          message: errorMessage,
          details: errorDetails,
          retryContent: lastMessageContentRef.current
        };
        setStreamError(errorState);

        // Reset streaming state
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setIsConnected(false);
        currentStreamingMessageRef.current = null;
      },

      onUpdateRPoint: (rPointCost: number, updatedBalance: string, timestamp: number) => {

        // Emit RPoint update event để các component khác có thể listen
        if (config.onRPointUpdate) {
          config.onRPointUpdate(rPointCost, updatedBalance, timestamp);
        }
      },

      onError: (error: Error) => {
        console.error('[useChatStream] 🚨 SSE ERROR:', error);

        // Xử lý timeout error đặc biệt - chỉ hiển thị nếu thực sự timeout
        if (error.message.includes('timeout')) {
          console.warn('[useChatStream] ⏰ SSE timeout detected');

          // Kiểm tra xem có đang streaming không - nếu có thì có thể là false positive
          if (isStreaming || isLoading || isThinking) {
            console.warn('[useChatStream] ⚠️ Timeout detected but still streaming - ignoring timeout error');
            return; // Không set error nếu đang streaming
          }

          setError('Kết nối bị timeout sau 1 phút không có phản hồi. Vui lòng thử lại.');
        } else {
          setError(error.message);
        }

        setIsStreaming(false);
        setIsLoading(false); // Tắt loading khi có lỗi SSE
        setIsThinking(false);
        setIsConnected(false);
      },
      
      onClose: () => {
        console.log('[useChatStream] SSE Closed');
        setIsConnected(false);
      }
    });

    return () => {
      // Cleanup
      // sseServiceRef.current?.disconnect();
    };
  }, [config, currentRole, isLoading, isStreaming, threadId, currentAgentId, isThinking]);

  /**
   * Load messages history từ API
   *
   * IMPORTANT: API trả về messages với sortDirection: 'DESC' (mới nhất trước)
   * Function này sẽ reverse thứ tự để có chronological order (cũ nhất trước)
   */
  const loadMessagesHistory = useCallback(async (
    page: number,
    isLoadMore: boolean = false
  ): Promise<void> => {
    if (!threadId || !apiServiceRef.current) {
      return;
    }

    // Tránh concurrent calls
    if (isLoadingHistory || isLoadingMoreHistory || isHistoryLoadingRef.current) {
      console.log('[useChatStream] History already loading, skipping...');
      return;
    }

    isHistoryLoadingRef.current = true;

    try {
      if (isLoadMore) {
        setIsLoadingMoreHistory(true);
      } else {
        setIsLoadingHistory(true);
      }
      setHistoryError(null);

      const query: GetMessagesQuery = {
        page,
        limit: config.messageHistory?.pageSize || 20,
        sortBy: 'createdAt',
        sortDirection: 'DESC', // Mới nhất trước
      };

      // Sử dụng singleton để tránh duplicate calls
      const response: GetMessagesResponseData = await chatApiSingleton.executeGetMessages(
        threadId,
        async () => {
          return apiServiceRef.current!.getMessages(threadId, query);
        }
      );

      // Convert ThreadMessageResponseDto thành ChatMessage (API mới)
      const convertedMessages = convertThreadMessagesToChatMessages(response.items);

      // API trả về DESC (mới nhất trước), cần reverse để có chronological order (cũ nhất trước)
      const newChatMessages = convertedMessages.reverse();

      if (isLoadMore) {
        // Load more history = load tin nhắn cũ hơn
        // Append vào đầu danh sách (vì đây là tin nhắn cũ hơn)
        setHistoryMessages(prev => {
          const updated = [...newChatMessages, ...prev];
          console.log('[useChatStream] Updated history messages (load more):', {
            newCount: newChatMessages.length,
            prevCount: prev.length,
            totalCount: updated.length,
            firstNew: newChatMessages[0]?.timestamp,
            lastPrev: prev[prev.length - 1]?.timestamp
          });
          return updated;
        });
      } else {
        // Replace toàn bộ danh sách với thứ tự chronological
        console.log('[useChatStream] Setting initial history messages:', {
          count: newChatMessages.length,
          firstMessage: newChatMessages[0]?.timestamp,
          lastMessage: newChatMessages[newChatMessages.length - 1]?.timestamp
        });
        setHistoryMessages(newChatMessages);
      }

      // Update pagination state
      setCurrentHistoryPage(response.meta.currentPage);
      setTotalHistoryItems(response.meta.totalItems);
      setHasMoreHistory(response.meta.currentPage < response.meta.totalPages);

      if (config.debug) {
        console.log('[useChatStream] History messages loaded:', {
          threadId,
          page,
          itemCount: response.items.length,
          totalItems: response.meta.totalItems,
          hasMore: response.meta.currentPage < response.meta.totalPages,
        });
      }
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to load history messages:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load history messages';
      setHistoryError(errorMessage);
    } finally {
      setIsLoadingHistory(false);
      setIsLoadingMoreHistory(false);
      isHistoryLoadingRef.current = false;
    }
  }, [threadId, config.messageHistory?.pageSize, config.debug, isLoadingHistory, isLoadingMoreHistory]);

  /**
   * Load more history messages (next page)
   */
  const loadMoreHistory = useCallback(async (): Promise<void> => {
    if (!hasMoreHistory || isLoadingMoreHistory || isLoadingHistory) {
      return;
    }

    await loadMessagesHistory(currentHistoryPage + 1, true);
  }, [hasMoreHistory, isLoadingMoreHistory, isLoadingHistory, currentHistoryPage, loadMessagesHistory]);

  /**
   * Refresh history messages (reload từ đầu)
   */
  const refreshHistory = useCallback(async (): Promise<void> => {
    if (isLoadingHistory || isLoadingMoreHistory) {
      return;
    }

    setCurrentHistoryPage(1);
    await loadMessagesHistory(1, false);
  }, [isLoadingHistory, isLoadingMoreHistory, loadMessagesHistory]);

  /**
   * Reset history state
   */
  const resetHistory = useCallback(() => {
    setHistoryMessages([]);
    setIsLoadingHistory(false);
    setIsLoadingMoreHistory(false);
    setHasMoreHistory(true);
    setHistoryError(null);
    setCurrentHistoryPage(1);
    setTotalHistoryItems(0);
    currentThreadIdRef.current = null;
  }, []);

  /**
   * Tạo thread mới
   */
  const createNewThread = useCallback(async (name?: string): Promise<{ threadId: string; threadName: string }> => {
    if (!apiServiceRef.current) {
      setError('API service not initialized');
      throw new Error('API service not initialized');
    }

    try {
      setIsCreatingThread(true);
      setError(null);

      console.log('[useChatStream] Creating new thread, cancelling current operations...');

      // 1. Cancel current SSE and API calls if any
      if (sseServiceRef.current && isConnected) {
        console.log('[useChatStream] Disconnecting current SSE connection...');
        sseServiceRef.current.disconnect();
        setIsConnected(false);
      }

      // 2. Stop current streaming/loading if any
      if (isStreaming || isLoading) {
        console.log('[useChatStream] Stopping current streaming/loading...');
        if (currentRunId && apiServiceRef.current) {
          try {
            await apiServiceRef.current.stopRun(currentRunId);
          } catch (error) {
            console.warn('[useChatStream] Failed to stop current run:', error);
          }
        }
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentRunId(null);
        currentStreamingMessageRef.current = null;
      }

      // 3. Clear current state
      setMessages([]);
      setCurrentStreamingText('');
      setStreamError(null);
      currentStreamingMessageRef.current = null;
      lastMessageContentRef.current = '';

      // 4. Create new thread
      console.log('[useChatStream] Creating new thread via API...');
      const response = await apiServiceRef.current.createThread(name);

      // 5. Set new thread state
      setThreadId(response.threadId);
      setThreadName(response.name);

      console.log('[useChatStream] Thread created successfully:', response);

      // 6. Emit thread created event
      config.threadEvents?.onThreadCreated?.(response.threadId, response.name);

      return {
        threadId: response.threadId,
        threadName: response.name
      };
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to create thread:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create thread';
      setError(errorMessage);
      throw error;
    } finally {
      setIsCreatingThread(false);
    }
  }, [config.threadEvents, isConnected, isStreaming, isLoading, currentRunId]);

  /**
   * Load thread mới nhất (theo createdAt DESC)
   */
  const loadLatestThread = useCallback(async () => {
    if (!apiServiceRef.current) {
      setError('API service not initialized');
      return;
    }

    try {
      setIsLoadingThreads(true);
      setError(null);

      // Sử dụng singleton để tránh duplicate calls
      const response = await chatApiSingleton.executeGetThreads(async () => {
        return apiServiceRef.current!.getThreads({
          page: 1,
          limit: 1,
          sortBy: 'createdAt',
          sortDirection: 'DESC'
        });
      });

      if (response.items.length > 0) {
        const latestThread = response.items[0];
        setThreadId(latestThread!.threadId);
        setThreadName(latestThread!.name);

        console.log('[useChatStream] Loaded latest thread:', latestThread);
      } else {
        // Không có thread nào, tự động tạo thread mới
        console.log('[useChatStream] No threads found, creating new thread automatically...');
        try {
          // Tạo thread trực tiếp qua API service
          const response = await apiServiceRef.current!.createThread('Cuộc trò chuyện mới');
          setThreadId(response.threadId);
          setThreadName(response.name);

          console.log('[useChatStream] Successfully created initial thread:', response);

          // Emit thread created event
          config.threadEvents?.onThreadCreated?.(response.threadId, response.name);
        } catch (error) {
          console.error('[useChatStream] Failed to create initial thread:', error);
          // Vẫn set null để có thể tạo sau
          setThreadId(null);
          setThreadName(null);
        }
      }
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to load latest thread:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load latest thread';
      setError(errorMessage);

      // Không tạo thread mới ở đây, để user tự tạo khi cần
      setThreadId(null);
      setThreadName(null);
    } finally {
      setIsLoadingThreads(false);
    }
  }, [config.threadEvents]); // Add config.threadEvents dependency

  // Auto-load latest thread khi khởi tạo - chỉ chạy một lần
  useEffect(() => {
    // Kiểm tra singleton để tránh multiple initialization
    if (chatApiSingleton.isInitialized()) {
      console.log('[useChatStream] Already initialized via singleton, skipping...');
      return;
    }

    // Thêm timeout để tránh React StrictMode double execution
    const timeoutId = setTimeout(() => {
      if (!hasLoadedInitialThreadRef.current && apiServiceRef.current && !isLoadingThreads && !isCreatingThread) {
        console.log('[useChatStream] Auto-loading latest thread...');
        chatApiSingleton.setInitialized();
        hasLoadedInitialThreadRef.current = true;
        loadLatestThread();
      }
    }, 100); // Tăng delay lên 100ms

    return () => clearTimeout(timeoutId);
  }, [loadLatestThread, isLoadingThreads, isCreatingThread]);

  // Auto load history khi threadId thay đổi
  useEffect(() => {
    // Clear previous timeout
    if (historyTimeoutRef.current) {
      clearTimeout(historyTimeoutRef.current);
    }

    // Kiểm tra xem threadId có thay đổi thực sự không
    if (currentThreadIdRef.current === threadId) {
      console.log('[useChatStream] ThreadId unchanged, skipping history load...');
      return;
    }

    currentThreadIdRef.current = threadId;

    if (threadId && (config.messageHistory?.autoLoad ?? true)) {
      console.log('[useChatStream] Scheduling auto-load history for thread:', threadId);

      // Debounce API call
      historyTimeoutRef.current = setTimeout(() => {
        console.log('[useChatStream] Executing auto-load history for thread:', threadId);
        loadMessagesHistory(1, false);
      }, 100); // 100ms debounce
    } else if (!threadId) {
      console.log('[useChatStream] No threadId, resetting history...');
      resetHistory();
    }

    // Cleanup timeout on unmount
    return () => {
      if (historyTimeoutRef.current) {
        clearTimeout(historyTimeoutRef.current);
      }
    };
  }, [threadId, config.messageHistory?.autoLoad, loadMessagesHistory, resetHistory]);

  /**
   * Gửi tin nhắn - hỗ trợ cả legacy string và ChatMessageRequest mới
   */
  const sendMessage = useCallback(async (
    contentOrRequest: string | ChatMessageRequest,
    threadIdParam?: string,
    alwaysApproveToolCall: boolean = false
  ): Promise<ChatMessageResponse | null> => {
    if (!apiServiceRef.current || !sseServiceRef.current) {
      setError('Services not initialized');
      return null;
    }

    // ✅ Check localStorage for always approve setting
    const shouldAlwaysApprove = toolApprovalSettingsService.shouldAlwaysApprove(
      currentAgentId ? { agentId: currentAgentId } : {}
    );

    // Override alwaysApproveToolCall if localStorage setting is enabled
    const finalAlwaysApprove = shouldAlwaysApprove || alwaysApproveToolCall;

    if (shouldAlwaysApprove) {
      console.log('[useChatStream] 🔄 Always approve enabled from localStorage, setting alwaysApproveToolCall = true');
    }

    // Hủy streaming/loading hiện tại nếu có
    if (isStreaming || isLoading) {
      console.log('[useChatStream] Cancelling current operation before sending new message');
      try {
        await stopStreaming();
      } catch (error) {
        console.warn('[useChatStream] Failed to stop current operation:', error);
      }
    }

    // Xử lý theo loại request
    let isLegacyRequest = typeof contentOrRequest === 'string';
    let currentThreadId: string;
    let messageContent: string;

    if (isLegacyRequest) {
      // Legacy string request
      messageContent = contentOrRequest as string;
      currentThreadId = threadIdParam || threadId || '';

      // Tạo thread nếu chưa có
      if (!currentThreadId) {
        console.log('[useChatStream] No thread available, creating new thread...');
        try {
          const newThread = await createNewThread('Cuộc trò chuyện mới');
          currentThreadId = newThread.threadId;
          console.log('[useChatStream] Created new thread for message:', currentThreadId);
        } catch (error) {
          console.error('[useChatStream] Failed to create thread for message:', error);
          setError('Failed to create thread. Please try again.');
          return null;
        }
      }
    } else {
      // ChatMessageRequest object
      const messageRequest = contentOrRequest as ChatMessageRequest;
      currentThreadId = messageRequest.threadId || threadId || '';

      // ✅ Set alwaysApproveToolCall từ localStorage nếu chưa có
      if (messageRequest.alwaysApproveToolCall === undefined) {
        messageRequest.alwaysApproveToolCall = finalAlwaysApprove;
        console.log('[useChatStream] 🔄 Set alwaysApproveToolCall in ChatMessageRequest:', finalAlwaysApprove);
      }

      // Lấy content từ content block đầu tiên
      const firstTextBlock = messageRequest.contentBlocks?.find((block: any) => block.type === 'text');
      messageContent = firstTextBlock?.content || '';

      // Tạo thread nếu chưa có
      if (!currentThreadId) {
        console.log('[useChatStream] No thread available, creating new thread...');
        try {
          const newThread = await createNewThread('Cuộc trò chuyện mới');
          currentThreadId = newThread.threadId;
          messageRequest.threadId = currentThreadId;
          console.log('[useChatStream] Created new thread for message:', currentThreadId);
        } catch (error) {
          console.error('[useChatStream] Failed to create thread for message:', error);
          setError('Failed to create thread. Please try again.');
          return null;
        }
      }
    }

    try {
      setError(null);
      setStreamError(null);
      setIsLoading(true);

      // Lưu content để có thể retry
      lastMessageContentRef.current = messageContent;

      // Hiển thị tin nhắn user (chỉ khi không phải modify)
      const shouldShowUserMessage = isLegacyRequest || !(contentOrRequest as ChatMessageRequest).messageId;
      if (shouldShowUserMessage) {
        // Extract reply info từ request nếu có
        const replyToMessageId = !isLegacyRequest ? (contentOrRequest as ChatMessageRequest).replyToMessageId : undefined;

        const userMessage: ChatMessage = {
          id: uuidv4(),
          content: messageContent,
          sender: 'user',
          timestamp: new Date(),
          status: MessageStatus.SENT,
          threadId: currentThreadId,
          metadata: {
            ...(replyToMessageId && { replyToMessageId })
          }
        };
        setMessages(prev => [...prev, userMessage]);
      }

      // Chuẩn bị cho streaming
      currentStreamingMessageRef.current = null;
      setCurrentStreamingText('');
      tokenCountRef.current = 0;
      lastTokenTimeRef.current = Date.now();

      // Call API gửi tin nhắn
      console.log('[useChatStream] 📤 Calling API sendMessage...', {
        isLegacyRequest,
        threadId: currentThreadId,
        contentType: typeof contentOrRequest
      });

      const response = await apiServiceRef.current.sendMessage(
        contentOrRequest,
        isLegacyRequest ? currentThreadId : undefined,
        isLegacyRequest ? finalAlwaysApprove : undefined
      );

      console.log('[useChatStream] ✅ Message sent, response:', response);

      // Lưu runId từ response
      const newRunId = response.runId;
      setCurrentRunId(newRunId);
      console.log('[useChatStream] 🆔 RunId set:', newRunId);

      // Connect SSE để nhận stream
      console.log('[useChatStream] 🔌 Connecting SSE...', { threadId: currentThreadId, runId: newRunId });
      await sseServiceRef.current.connect(currentThreadId, newRunId);

      // Set current message ID cho queue-based streaming (sử dụng runId làm messageId tạm thời)
      sseServiceRef.current.setCurrentMessageId(newRunId);
      console.log('[useChatStream] ✅ SSE connected successfully and messageId set:', newRunId);
      console.log('[useChatStream] 🎯 Current state after SSE connect:', {
        isLoading,
        isStreaming,
        isConnected,
        currentRunId: newRunId
      });

      return response;

    } catch (error: unknown) {
      console.error('[useChatStream] Failed to send message:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
      setError(errorMessage);
      setIsStreaming(false);
      setIsLoading(false);
      setIsThinking(false);

      // Remove the failed AI message
      if (currentStreamingMessageRef.current) {
        setMessages(prev => prev.filter(msg => msg.id !== currentStreamingMessageRef.current?.id));
        currentStreamingMessageRef.current = null;
      }

      return null;
    }
  }, [threadId, createNewThread, isLoading, isStreaming]);



  /**
   * Dừng streaming hiện tại
   */
  const stopStreaming = useCallback(async () => {
    try {
      console.log('[useChatStream] 🛑 STOPPING STREAMING/LOADING...', {
        currentRunId,
        isStreaming,
        isLoading,
        isConnected
      });

      // 1. ✅ Reset state IMMEDIATELY để prevent further event processing
      console.log('[useChatStream] 🛑 Resetting state immediately...');
      setIsStreaming(false);
      setIsLoading(false);
      setIsThinking(false);
      setCurrentRunId(null);
      currentStreamingMessageRef.current = null;

      // 1.5. ✅ Clear any pending timeouts
      if (historyTimeoutRef.current) {
        clearTimeout(historyTimeoutRef.current);
        historyTimeoutRef.current = null;
        console.log('[useChatStream] 🛑 Cleared history timeout');
      }

      // 2. ✅ Disconnect SSE (luôn thực hiện)
      if (sseServiceRef.current && isConnected) {
        console.log('[useChatStream] 🛑 Disconnecting SSE connection...');
        sseServiceRef.current.disconnect();
        setIsConnected(false);
      }

      // 3. ✅ Call API để dừng run (chỉ khi có currentRunId và apiService)
      if (currentRunId && apiServiceRef.current) {
        console.log('[useChatStream] 🛑 Stopping current run via API:', currentRunId);
        await apiServiceRef.current.stopRun(currentRunId);
      } else {
        console.log('[useChatStream] 🛑 No currentRunId or apiService, skipping API call');
      }

      console.log('[useChatStream] ✅ Stop streaming completed successfully');

    } catch (error: unknown) {
      console.error('[useChatStream] ❌ Failed to stop streaming:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to stop streaming';
      setError(errorMessage);

      // ✅ Vẫn reset state ngay cả khi có lỗi
      setIsStreaming(false);
      setIsLoading(false);
      setIsThinking(false);
      setCurrentRunId(null);
      setIsConnected(false);
      currentStreamingMessageRef.current = null;
    }
  }, [currentRunId, isStreaming, isLoading, isConnected]);

  /**
   * Send retry message without creating new user message
   */
  const sendRetryMessage = useCallback(async (content: string) => {
    if (!threadId) {
      console.error('[useChatStream] No thread ID for retry');
      return;
    }

    try {
      setError(null);
      setStreamError(null); // Clear previous stream error
      setIsLoading(true); // Bắt đầu loading

      console.log('[useChatStream] 🔄 RETRYING message without creating new user message:', content);

      // 1. Chuẩn bị cho streaming (message sẽ được tạo khi nhận token đầu tiên)
      currentStreamingMessageRef.current = null;
      setCurrentStreamingText('');

      // Reset streaming state
      tokenCountRef.current = 0;
      lastTokenTimeRef.current = Date.now();

      // 2. Call API gửi tin nhắn (KHÔNG tạo user message trong UI)
      const response = await apiServiceRef.current!.sendMessage(content, threadId);

      console.log('[useChatStream] 🔄 Retry API response:', response);

      // 3. Lưu runId và bắt đầu streaming
      setCurrentRunId(response.runId);
      setIsLoading(false); // Tắt loading, bắt đầu streaming
      setIsStreaming(true);

      // 4. Connect SSE để nhận streaming response
      console.log('[useChatStream] 🔄 Connecting SSE for retry...', { threadId, runId: response.runId });
      await sseServiceRef.current!.connect(threadId, response.runId);

      // Set current message ID cho queue-based streaming
      sseServiceRef.current!.setCurrentMessageId(response.runId);
      console.log('[useChatStream] 🔄 SSE connected successfully for retry and messageId set:', response.runId);

      console.log('[useChatStream] 🔄 Retry initiated successfully');
    } catch (error) {
      console.error('[useChatStream] ❌ Retry failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to retry message');
      setIsLoading(false);
      setIsStreaming(false);
      setIsThinking(false);
    }
  }, [threadId]);

  /**
   * Retry last message khi có stream error
   */
  const retryLastMessage = useCallback(async () => {
    if (!streamError?.retryContent) {
      console.warn('[useChatStream] ⚠️ No retry content available');
      return;
    }

    console.log('[useChatStream] 🔄 RETRY BUTTON CLICKED - Starting retry process');
    console.log('[useChatStream] 🔄 Retry content:', streamError.retryContent);
    console.log('[useChatStream] 🔄 Current messages count:', messages.length);

    // Clear stream error và retry
    setStreamError(null);

    console.log('[useChatStream] 🔄 Calling sendRetryMessage (no new user message)');
    await sendRetryMessage(streamError.retryContent);
  }, [streamError, sendRetryMessage, messages.length]);

  /**
   * Load specific thread by ID
   */
  const loadSpecificThread = useCallback(async (targetThreadId: string) => {
    try {
      setIsLoadingThreads(true);
      setError(null);

      console.log('[useChatStream] Loading specific thread:', targetThreadId);

      // Check if thread is deleted before making API call
      if (isThreadDeleted(targetThreadId)) {
        console.warn('[useChatStream] Thread is marked as deleted, skipping load:', targetThreadId);
        throw new Error(`Thread ${targetThreadId} has been deleted`);
      }

      // Verify thread exists by getting its detail using ThreadsService
      try {
        console.log('[useChatStream] Loading specific thread:', targetThreadId);
        const threadDetail = await ThreadsService.getThreadDetail(targetThreadId);

        if (threadDetail) {
          // Clear current messages
          setMessages([]);
          setCurrentStreamingText('');
          currentStreamingMessageRef.current = null;
          setStreamError(null);

          // Set new thread
          setThreadId(targetThreadId);
          setThreadName(threadDetail.name);

          // Emit thread loaded event
          config.threadEvents?.onThreadLoaded?.(targetThreadId, threadDetail.name);

          console.log('[useChatStream] Loaded specific thread successfully:', threadDetail);
        } else {
          throw new Error('Thread not found');
        }
      } catch (apiError: unknown) {
        // Nếu thread không tồn tại (404), có thể đã bị xóa
        if (apiError instanceof Error && apiError.message.includes('404')) {
          console.warn('[useChatStream] Thread not found (possibly deleted):', targetThreadId);
          throw new Error(`Thread ${targetThreadId} not found (possibly deleted)`);
        }
        // Re-throw other errors
        throw apiError;
      }
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to load specific thread:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load thread';
      setError(errorMessage);
    } finally {
      setIsLoadingThreads(false);
    }
  }, [config.threadEvents]);

  /**
   * Get current thread ID
   */
  const getCurrentThreadId = useCallback((): string | null => {
    return threadId;
  }, [threadId]);

  /**
   * Update thread name
   */
  const updateThreadName = useCallback(async (targetThreadId: string, newName: string) => {
    if (!apiServiceRef.current) {
      setError('API service not initialized');
      throw new Error('API service not initialized');
    }

    try {
      console.log('[useChatStream] Updating thread name:', { targetThreadId, newName });

      // Update via ThreadsService
      const response = await ThreadsService.updateThread(targetThreadId, { name: newName });

      // Update local state if this is current thread
      if (threadId === targetThreadId) {
        console.log('[useChatStream] Updating local thread name:', {
          oldName: threadName,
          newName: response.name,
          threadId,
          targetThreadId
        });
        setThreadName(response.name);
      } else {
        console.log('[useChatStream] Not updating local state - different thread:', {
          currentThreadId: threadId,
          targetThreadId
        });
      }

      // Emit thread name changed event
      config.threadEvents?.onThreadNameChanged?.(targetThreadId, response.name);

      console.log('[useChatStream] Thread name updated:', response);
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to update thread name:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update thread name';
      setError(errorMessage);
      throw error;
    }
  }, [threadId, threadName, config.threadEvents]);

  /**
   * Switch to a different thread (disconnect current SSE and load new thread)
   */
  const switchToThread = useCallback(async (newThreadId: string) => {
    try {
      console.log('[useChatStream] Switching to thread:', newThreadId);
      console.log('[useChatStream] Current thread:', threadId);

      // Nếu đã là thread hiện tại, không cần làm gì
      if (threadId === newThreadId) {
        console.log('[useChatStream] Already on target thread, skipping switch');
        return;
      }

      const previousThreadId = threadId;

      // 1. Disconnect SSE hiện tại nếu có
      if (sseServiceRef.current && isConnected) {
        console.log('[useChatStream] Disconnecting current SSE connection...');
        sseServiceRef.current.disconnect();
        setIsConnected(false);
      }

      // 2. Stop streaming hiện tại nếu có
      if (isStreaming || isLoading) {
        console.log('[useChatStream] Stopping current streaming/loading...');
        if (currentRunId && apiServiceRef.current) {
          try {
            await apiServiceRef.current.stopRun(currentRunId);
          } catch (error) {
            console.warn('[useChatStream] Failed to stop current run:', error);
          }
        }
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentRunId(null);
        currentStreamingMessageRef.current = null;
      }

      // 3. Clear current messages và state
      console.log('[useChatStream] Clearing current messages and state...');
      setMessages([]);
      setCurrentStreamingText('');
      setStreamError(null);
      setError(null);
      currentStreamingMessageRef.current = null;
      lastMessageContentRef.current = '';

      // 4. Load new thread
      console.log('[useChatStream] Loading new thread:', newThreadId);
      try {
        await loadSpecificThread(newThreadId);

        // 5. Emit thread switched event
        if (previousThreadId) {
          config.threadEvents?.onThreadSwitched?.(previousThreadId, newThreadId);
        }

        console.log('[useChatStream] Successfully switched to thread:', newThreadId);
      } catch (loadError: unknown) {
        // Nếu không load được thread (có thể đã bị xóa), clear state
        console.error('[useChatStream] Failed to load target thread, clearing state:', loadError);
        setThreadId(null);
        setThreadName(null);

        // Emit error event
        const errorMessage = loadError instanceof Error ? loadError.message : 'Failed to load thread';
        setError(errorMessage);

        // Vẫn emit switched event để parent component biết thread switch failed
        if (previousThreadId) {
          config.threadEvents?.onThreadSwitched?.(previousThreadId, '');
        }

        throw loadError;
      }
    } catch (error: unknown) {
      console.error('[useChatStream] Failed to switch thread:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to switch thread';
      setError(errorMessage);
    }
  }, [threadId, isConnected, isStreaming, isLoading, currentRunId, loadSpecificThread, config.threadEvents]);

  /**
   * Clear tất cả messages và reset thread
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
    setCurrentStreamingText('');
    setThreadId(null);
    setThreadName(null);
    currentStreamingMessageRef.current = null;
    lastMessageContentRef.current = '';
    setStreamError(null);

    // Reset flag để có thể load thread mới
    hasLoadedInitialThreadRef.current = false;
    // Reset message history
    resetHistory();
  }, [resetHistory]);

  // Assign stopStreaming to ref để có thể gọi từ sendMessage
  useEffect(() => {
    stopStreamingRef.current = stopStreaming;
  }, [stopStreaming]);

  /**
   * Approve tool call với decision
   */
  const approveToolCall = useCallback(async (decision: 'yes' | 'no' | 'always') => {
    if (!toolCallInterrupt || !apiServiceRef.current || !sseServiceRef.current) {
      console.warn('[useChatStream] No tool call interrupt to approve');
      return;
    }

    try {
      console.log('[useChatStream] 🔧 Approving tool call:', {
        decision,
        toolName: toolCallInterrupt.toolName,
        threadId: toolCallInterrupt.threadId
      });

      // Call approve API
      const response = await apiServiceRef.current.approveToolCall(toolCallInterrupt.threadId, decision);

      console.log('[useChatStream] 🔧 Tool call approval response:', response);

      // Clear tool call interrupt state
      setToolCallInterrupt(null);

      // Reconnect SSE với runId mới nếu có
      if (response.runId && response.runId !== currentRunId) {
        console.log('[useChatStream] 🔄 Reconnecting SSE with new runId:', {
          oldRunId: currentRunId,
          newRunId: response.runId,
          threadId: toolCallInterrupt.threadId
        });

        // Disconnect current SSE
        console.log('[useChatStream] 🔌 Disconnecting current SSE...');
        sseServiceRef.current.disconnect();

        // Update runId
        const oldRunId = currentRunId;
        setCurrentRunId(response.runId);
        console.log('[useChatStream] 🆔 Updated runId state:', {
          oldRunId,
          newRunId: response.runId,
          stateUpdated: true
        });

        // Reset streaming states để chuẩn bị cho stream mới
        setIsStreaming(false);
        setIsLoading(true);
        console.log('[useChatStream] 🔄 Reset streaming states for new connection');

        // Reconnect với runId mới
        console.log('[useChatStream] 🔌 Connecting to new SSE...');
        try {
          await sseServiceRef.current.connect(toolCallInterrupt.threadId, response.runId);
          console.log('[useChatStream] ✅ SSE reconnected with new runId successfully');

          // Verify connection state
          console.log('[useChatStream] 🔍 Connection verification completed for:', {
            threadId: toolCallInterrupt.threadId,
            newRunId: response.runId
          });

          // Wait a bit for server to start sending events
          console.log('[useChatStream] ⏳ Waiting for server to start sending events...');
          setTimeout(() => {
            console.log('[useChatStream] ⏰ Ready to receive events after tool approval');
          }, 1000);
        } catch (connectError) {
          console.error('[useChatStream] ❌ Failed to reconnect SSE:', connectError);
          setError('Failed to reconnect to streaming service');
        }
      } else {
        console.log('[useChatStream] ⚠️ No runId in response or same runId:', {
          responseRunId: response.runId,
          currentRunId: currentRunId,
          hasRunId: !!response.runId,
          isSameRunId: response.runId === currentRunId
        });
      }

      console.log('[useChatStream] ✅ Tool call approved successfully');
    } catch (error) {
      console.error('[useChatStream] Failed to approve tool call:', error);
      setError(error instanceof Error ? error.message : 'Failed to approve tool call');
    }
  }, [toolCallInterrupt, currentRunId]);

  /**
   * Dismiss tool call interrupt without approval
   */
  const dismissToolCallInterrupt = useCallback(() => {
    console.log('[useChatStream] 🚫 Dismissing tool call interrupt');
    setToolCallInterrupt(null);
  }, []);

  return {
    // State
    messages,
    isStreaming,
    isLoading,
    isThinking,
    currentStreamingText,
    currentRunId,
    threadId,
    threadName,
    isCreatingThread,
    isLoadingThreads,

    // Worker thinking state
    workerThinking,

    // Tool call interrupt state
    toolCallInterrupt,
    approveToolCall,
    dismissToolCallInterrupt,

    // Message History
    historyMessages,
    isLoadingHistory,
    isLoadingMoreHistory,
    hasMoreHistory,
    historyError,
    totalHistoryItems,

    // Actions
    sendMessage,
    stopStreaming,
    clearMessages,
    createNewThread,
    loadLatestThread,
    loadSpecificThread,
    switchToThread,
    getCurrentThreadId,
    updateThreadName,
    retryLastMessage,

    // Message History Actions
    loadMoreHistory,
    refreshHistory,

    // Status
    isConnected,
    error,
    streamError
  };
}

