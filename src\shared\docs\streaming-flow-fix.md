# Streaming Flow Fix - Proper Event Sequence Handling

Tài liệu này mô tả fix cho vấn đề xử lý sequence của SSE events.

## ❌ Vấn đề đã phát hiện

### SSE Event Sequence
```
1. stream_text_token (role="supervisor") → Stream text
2. llm_stream_end (role="supervisor") → Finalize message  
3. update_rpoint → Trừ R-point
4. message_created (role="assistant") → Gán messageId
5. stream_session_end → Cleanup
```

### Vấn đề trong Logic
1. **Double Finalization**: `onLLMStreamEnd` finalize message, rồi `onStreamSessionEnd` lại finalize lần nữa
2. **MessageId Assignment Timing**: `message_created` event đến sau khi message đã được finalize
3. **Reference Loss**: `currentStreamingMessageRef` bị clear trước khi messageId được assign

## ✅ Giải pháp đã implement

### Fix 1: Proper LLM Stream End Handling

**Tr<PERSON>ớ<PERSON> đây**: Finalize hoàn toàn trong `onLLMStreamEnd`
```typescript
// ❌ Finalize hoàn toàn quá sớm
const finalMessage: ChatMessage = {
  ...currentStreamingMessageRef.current,
  status: MessageStatus.COMPLETED,
  timestamp: new Date(), // ← Final timestamp quá sớm
  metadata: { /* ... */ }
};
```

**Sau khi sửa**: Chỉ mark completed, chờ messageId
```typescript
// ✅ Mark completed nhưng giữ reference
const updatedMessage: ChatMessage = {
  ...currentStreamingMessageRef.current,
  status: MessageStatus.COMPLETED, // Mark as completed but keep in currentStreamingMessageRef
  metadata: {
    ...currentStreamingMessageRef.current.metadata,
    processingTime: Date.now() - currentStreamingMessageRef.current.timestamp.getTime(),
    llmStreamEndRole: role as any // Track role từ llm_stream_end
  }
};

// ✅ Keep reference để message_created có thể assign messageId
currentStreamingMessageRef.current = updatedMessage;
```

### Fix 2: Enhanced Message Created Handling

**Trước đây**: Chỉ assign messageId
```typescript
// ❌ Chỉ assign messageId, không finalize
const updatedMessage: ChatMessage = {
  ...currentStreamingMessageRef.current,
  messageId: messageId,
  metadata: { /* ... */ }
};
```

**Sau khi sửa**: Assign messageId và finalize hoàn toàn
```typescript
// ✅ Assign messageId và finalize hoàn toàn
const finalMessage: ChatMessage = {
  ...currentStreamingMessageRef.current,
  messageId: messageId, // Gán messageId từ API
  status: MessageStatus.COMPLETED, // Ensure message is completed
  timestamp: new Date(), // Update final timestamp
  metadata: {
    ...currentStreamingMessageRef.current.metadata,
    apiMessageId: messageId,
    contentPreview: contentPreview,
    messageCreatedRole: role, // Track role từ message_created event
    finalizedAt: new Date().toISOString() // Track when message was finalized
  }
};
```

### Fix 3: Stream Session End Cleanup

**Trước đây**: Double finalization
```typescript
// ❌ Finalize lại lần nữa
if (currentStreamingMessageRef.current) {
  const finalMessage: ChatMessage = {
    ...currentStreamingMessageRef.current,
    status: MessageStatus.COMPLETED,
    timestamp: new Date()
  };
  // Update messages again
}
```

**Sau khi sửa**: Chỉ cleanup state
```typescript
// ✅ KHÔNG finalize message ở đây nữa vì đã được finalize trong onMessageCreated
console.log('[useChatStream] 🏁 Current message status:', {
  hasMessage: !!currentStreamingMessageRef.current,
  messageId: currentStreamingMessageRef.current?.id,
  status: currentStreamingMessageRef.current?.status,
  hasApiMessageId: !!currentStreamingMessageRef.current?.metadata?.apiMessageId
});

// Chỉ cleanup state
setIsStreaming(false);
setCurrentStreamingText('');
currentStreamingMessageRef.current = null;
```

## 🔍 Event Flow Comparison

### Before Fix (Problematic)
```
1. stream_text_token → Create/update message
2. llm_stream_end → ❌ Finalize message (timestamp set, reference kept)
3. message_created → ❌ Assign messageId to already finalized message
4. stream_session_end → ❌ Finalize again (double finalization)
```

### After Fix (Correct)
```
1. stream_text_token → Create/update message
2. llm_stream_end → ✅ Mark completed, keep reference, add metadata
3. message_created → ✅ Assign messageId + finalize completely
4. stream_session_end → ✅ Only cleanup state
```

## 🎯 Expected Results

### 1. Single Finalization
- Message chỉ được finalize một lần trong `onMessageCreated`
- `onLLMStreamEnd` chỉ mark completed
- `onStreamSessionEnd` chỉ cleanup state

### 2. Proper MessageId Assignment
- MessageId được assign đúng thời điểm
- Message được finalize sau khi có messageId
- Reference được giữ cho đến khi finalize hoàn toàn

### 3. Complete Metadata Tracking
- `llmStreamEndRole`: Role từ llm_stream_end event
- `messageCreatedRole`: Role từ message_created event  
- `finalizedAt`: Timestamp khi message được finalize hoàn toàn
- `processingTime`: Thời gian xử lý từ start đến llm_stream_end

## 🔧 Debug Information

### Check Event Sequence
```javascript
// Monitor event sequence in console
// Look for these logs in order:
// 1. "🔤 TEXT TOKEN RECEIVED"
// 2. "🏁 LLM Stream End for role: supervisor - waiting for messageId"
// 3. "🆔 MESSAGE CREATED event"
// 4. "✅ MessageId assigned and message finalized"
// 5. "🏁 STREAM SESSION END - Cleaning up all streaming state"
```

### Check Message State
```javascript
// Check message metadata for proper tracking
const message = messages[messages.length - 1];
console.log({
  messageId: message.messageId,
  status: message.status,
  llmStreamEndRole: message.metadata?.llmStreamEndRole,
  messageCreatedRole: message.metadata?.messageCreatedRole,
  finalizedAt: message.metadata?.finalizedAt,
  processingTime: message.metadata?.processingTime
});
```

### Expected Metadata Structure
```javascript
{
  messageId: "fbf627e2-7225-4fd0-af27-83aaa5459388",
  status: "completed",
  metadata: {
    originalRole: "supervisor",
    streamingRole: "supervisor", 
    llmStreamEndRole: "supervisor",
    messageCreatedRole: "assistant",
    apiMessageId: "fbf627e2-7225-4fd0-af27-83aaa5459388",
    processingTime: 1234,
    finalizedAt: "2024-01-15T10:30:45.123Z"
  }
}
```

## 🚨 Troubleshooting

### Issue 1: MessageId not assigned
- Check if `message_created` event is being received
- Verify `currentStreamingMessageRef.current` exists when event arrives
- Check console for "🆔 MESSAGE CREATED event"

### Issue 2: Double finalization
- Check if message is being finalized multiple times
- Look for duplicate "✅ Message finalized" logs
- Verify `onStreamSessionEnd` doesn't finalize again

### Issue 3: Missing metadata
- Check if all tracking fields are present
- Verify role information is preserved
- Check timestamp and processing time calculation

## 🎯 Testing

### Test Cases
1. **Normal Flow**: stream_text_token → llm_stream_end → message_created → stream_session_end
2. **MessageId Assignment**: Verify messageId is assigned correctly
3. **Metadata Tracking**: Check all metadata fields are populated
4. **Single Finalization**: Ensure message is finalized only once

### Expected Behavior
- Single continuous message with proper messageId
- Complete metadata tracking
- No double finalization
- Clean state cleanup

Với fix này, streaming flow sẽ xử lý đúng sequence của SSE events và đảm bảo message được finalize đúng cách!
