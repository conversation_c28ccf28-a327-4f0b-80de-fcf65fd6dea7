import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { TFunction } from 'i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Typography,
  RandomColorIconCard,
  IconCard,
} from '@/shared/components/common';
import { z } from 'zod';

// Schema factory cho form với hỗ trợ i18n
const createFormSchema = (t: TFunction) => z.object({
  name: z.string().min(1, t('marketing:tags.validation.nameRequired', 'Tên tag là bắt buộc')),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, t('marketing:tags.validation.colorInvalid', 'Mã màu phải có định dạng HEX (ví dụ: #FF0000)'))
    .optional(),
});

export type TagFormValues = z.infer<ReturnType<typeof createFormSchema>>;

interface TagFormProps {
  mode?: 'create' | 'edit';
  initialData?: {
    id?: number;
    name?: string;
    color?: string;
  };
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
}

/**
 * Component form thêm/sửa tag
 */
const TagForm: React.FC<TagFormProps> = ({
  mode = 'create',
  initialData,
  onSubmit,
  onCancel
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    color: initialData?.color || '#FF6B6B'
  });

  // Tạo schema với hỗ trợ i18n
  const formSchema = React.useMemo(() => createFormSchema(t), [t]);

  // Cập nhật formData khi initialData thay đổi
  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name || '',
        color: initialData.color || '#FF6B6B'
      });
    }
  }, [initialData]);

  // Xử lý thay đổi màu từ ColorPicker
  const handleColorChange = (color: string) => {
    setFormData(prev => ({ ...prev, color }));
  };

  // Xử lý thay đổi màu từ RandomColorIconCard
  const handleRandomColorGenerated = (color: string) => {
    setFormData(prev => ({ ...prev, color }));
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Sử dụng color từ formData vì đây là state được cập nhật từ color picker
    onSubmit({
      ...values,
      color: formData.color
    });
  };

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        {mode === 'edit'
          ? t('marketing:tags.edit', 'Sửa tag')
          : t('marketing:tags.addNew', 'Thêm tag mới')
        }
      </Typography>

      <Form
        schema={formSchema}
        onSubmit={handleSubmit}
        className="space-y-4"
        defaultValues={{
          name: formData.name,
          color: formData.color
        }}
        useDefaultValuesOnce={false}
        key={`${mode}-${initialData?.id || 'new'}`}
      >
        <div className="grid grid-cols-1 gap-4">
          <FormItem name="name" label={t('marketing:tags.form.name', 'Tên tag')} required>
            <Input
              placeholder={t('marketing:tags.form.namePlaceholder', 'Nhập tên tag')}
              fullWidth
            />
          </FormItem>

          <FormItem name="color" label={t('marketing:tags.form.color', 'Mã màu')}>
            <div className="flex items-center space-x-3">
              {/* Color preview button */}
              <div
                className="w-12 h-10 rounded-lg border-2 border-border cursor-pointer hover:scale-105 transition-transform shadow-sm"
                style={{ backgroundColor: formData.color }}
                onClick={() => {
                  // Trigger native color picker
                  const input = document.createElement('input');
                  input.type = 'color';
                  input.value = formData.color;
                  input.onchange = (e) => {
                    const target = e.target as HTMLInputElement;
                    handleColorChange(target.value);
                  };
                  input.click();
                }}
                title={`${t('marketing:tags.form.colorPlaceholder', 'Chọn màu cho tag')}: ${formData.color}`}
              />

              {/* Random color button */}
              <RandomColorIconCard
                onColorGenerated={handleRandomColorGenerated}
                size="md"
                variant="ghost"
              />

              {/* Color code display */}
              <span className="text-sm text-muted-foreground font-mono">
                {formData.color}
              </span>
            </div>
          </FormItem>
        </div>

        <div className="flex justify-end space-x-2">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel', 'Hủy')}
            onClick={onCancel}
          />
          <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={t('common:save', 'Lưu')}
            onClick={() => {
              const form = document.querySelector('form');
              if (form) {
                form.dispatchEvent(
                  new Event('submit', { cancelable: true, bubbles: true })
                );
              }
            }}
          />
        </div>
      </Form>
    </Card>
  );
};

export default TagForm;
