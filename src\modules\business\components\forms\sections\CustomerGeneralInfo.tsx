import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import {
  Typography,
  Chip,
  Avatar,
  Icon,
  Button,
  Input,
  FormItem,
  Form,
  CollapsibleCard,
  IconCard,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { useFormErrors } from '@/shared/hooks/form';
import { NotificationUtil } from '@/shared/utils/notification';
import { CustomerDetailData } from './types';
import { useUpdateCustomerBasicInfo, useUpdateCustomerInformation } from '../../../hooks/useCustomerQuery';
import { UpdateCustomerBasicInfoDto } from '../../../services/customer.service';
import {
  UpdateCustomerBasicInfoFormValues
} from '../../../schemas/customer.schema';
import {
  UpdateCustomerInformationDto,
  UpdateCustomerBasicInfoDto
} from '../../../services/customer.service';
import { UserConvertCustomerListItemDto } from '../../../types/customer.types';

interface CustomerGeneralInfoProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị thông tin chung của khách hàng
 */
const CustomerGeneralInfo: React.FC<CustomerGeneralInfoProps> = ({ customer }) => {
  const { t } = useTranslation(['business', 'common']);
  const formRef = useRef<FormRef<FieldValues>>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Hooks
  const { setFormErrors } = useFormErrors<UpdateCustomerBasicInfoFormValues>();
  const updateBasicInfoMutation = useUpdateCustomerBasicInfo();
  const updateInformationMutation = useUpdateCustomerInformation();

  // State
  const [formData, setFormData] = useState<CustomerDetailData>(customer);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [newTag, setNewTag] = useState('');
  const [originalTags, setOriginalTags] = useState<string[]>(customer.tags || []);

  // Derived state
  const isSaving = updateBasicInfoMutation.isPending || updateInformationMutation.isPending;
  const hasTagsChanged = JSON.stringify(formData.tags || []) !== JSON.stringify(originalTags);

  // Effect để cập nhật state khi customer prop thay đổi
  useEffect(() => {
    setFormData(customer);
    setOriginalTags(customer.tags || []);
  }, [customer]);

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('vi-VN');
    } catch {
      return dateString;
    }
  };

  // Get status chip variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'blocked':
        return 'danger';
      default:
        return 'default';
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Hoạt động';
      case 'inactive':
        return 'Không hoạt động';
      case 'blocked':
        return 'Bị khóa';
      default:
        return status;
    }
  };

  // Handle input change
  const handleInputChange = (field: keyof CustomerDetailData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle add tag
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()],
      }));
      setNewTag('');
    }
  };

  // Handle remove tag
  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || [],
    }));
  };

  // State cho avatar file
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  // Handle avatar upload
  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      NotificationUtil.error({
        message: 'Vui lòng chọn file hình ảnh'
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      NotificationUtil.error({
        message: 'Kích thước file không được vượt quá 5MB'
      });
      return;
    }

    setIsUploadingAvatar(true);

    try {
      // Create preview URL
      const previewUrl = URL.createObjectURL(file);

      // Update form data with new avatar preview
      setFormData(prev => ({
        ...prev,
        avatar: previewUrl,
      }));

      // Store file for later upload
      setAvatarFile(file);

      console.log('Avatar selected:', file.name);
    } catch (error) {
      console.error('Error processing avatar:', error);
      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi xử lý avatar'
      });
    } finally {
      setIsUploadingAvatar(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle avatar click
  const handleAvatarClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle save
  const handleSave = async () => {
    try {
      // Prepare avatar file data if available
      let avatarFileData = undefined;
      if (avatarFile) {
        avatarFileData = {
          fileName: avatarFile.name,
          mimeType: avatarFile.type
        };
      }

      let response;

      if (hasTagsChanged) {
        // Nếu có thay đổi tags, sử dụng API /information
        const informationData: Record<string, unknown> = {
          tags: formData.tags || []
        };

        if (formData.name?.trim()) {
          informationData['name'] = formData.name.trim();
        }

        if (formData.phone?.trim()) {
          informationData['phone'] = formData.phone.trim();
        }

        if (formData.email?.trim()) {
          informationData['email'] = {
            primary: formData.email.trim(),
            secondary: ''
          };
        }

        if (formData.address?.trim()) {
          informationData['address'] = formData.address.trim();
        } else {
          informationData['address'] = null;
        }

        if (avatarFileData) {
          informationData['avatarFile'] = avatarFileData;
        }

        const mutationParams = {
          id: parseInt(formData.id),
          data: informationData,
          ...(avatarFile && { avatarFile })
        };

        response = await updateInformationMutation.mutateAsync(mutationParams);

        // Update original tags after successful save
        setOriginalTags(formData.tags || []);
      } else {
        // Nếu không có thay đổi tags, sử dụng API /basic-info
        const dataToValidate: Record<string, unknown> = {};

        if (formData.name?.trim()) {
          dataToValidate['name'] = formData.name.trim();
        }

        if (formData.phone?.trim()) {
          dataToValidate['phone'] = formData.phone.trim();
        }

        if (formData.email?.trim()) {
          dataToValidate['email'] = {
            primary: formData.email.trim(),
            secondary: ''
          };
        }

        if (formData.address?.trim()) {
          dataToValidate['address'] = formData.address.trim();
        }

        if (avatarFileData) {
          dataToValidate['avatarFile'] = avatarFileData;
        }

        const validatedData = basicInfoSchema.parse(dataToValidate);

        const basicMutationParams = {
          id: parseInt(formData.id),
          data: validatedData as UpdateCustomerBasicInfoDto,
          ...(avatarFile && { avatarFile })
        };

        response = await updateBasicInfoMutation.mutateAsync(basicMutationParams);
      }

      // Update local state on success
      setFormData(prev => {
        const updatedData: Partial<CustomerDetailData> = {
          ...prev,
          name: formData.name,
          phone: formData.phone,
          email: formData.email,
        };

        // Chỉ thêm address nếu có giá trị
        if (formData.address) {
          updatedData.address = formData.address;
        }

        // Chỉ thêm tags nếu có giá trị
        if (formData.tags) {
          updatedData.tags = formData.tags;
        }

        // Update avatar với priority: avatarUpload > avatar > prev.avatar
        const responseResult = response.result as unknown as Record<string, unknown>;
        if (responseResult?.['avatarUpload'] && typeof responseResult['avatarUpload'] === 'object') {
          const avatarUpload = responseResult['avatarUpload'] as Record<string, unknown>;
          if (avatarUpload['publicUrl'] && typeof avatarUpload['publicUrl'] === 'string') {
            updatedData.avatar = avatarUpload['publicUrl'];
          }
        } else if (responseResult?.['avatar'] && typeof responseResult['avatar'] === 'string') {
          updatedData.avatar = responseResult['avatar'];
        } else if (prev.avatar) {
          updatedData.avatar = prev.avatar;
        }

        return updatedData as CustomerDetailData;
      });

      // Clear avatar file after successful save
      setAvatarFile(null);

      // Show success notification
      NotificationUtil.success({
        message: 'Cập nhật thông tin khách hàng thành công'
      });

    } catch (error: unknown) {
      console.error('Error saving customer:', error);

      // Handle Zod validation errors
      if (error && typeof error === 'object' && 'errors' in error) {
        const zodError = error as { errors: Array<{ message: string; path: string[]; code: string }> };
        const formErrors: Record<string, string> = {};

        zodError.errors.forEach((err) => {
          // Handle nested paths like ['email', 'primary']
          if (err.path.length > 0) {
            const mainField = err.path[0];

            // Map nested email errors to the main email field
            if (mainField === 'email') {
              formErrors['email'] = err.message;
            } else if (typeof mainField === 'string') {
              formErrors[mainField] = err.message;
            }
          }
        });

        setFormErrors(formErrors);

        // Show notification for validation errors
        NotificationUtil.error({
          message: 'Vui lòng kiểm tra lại thông tin đã nhập'
        });
      } else {
        // Handle other types of errors
        NotificationUtil.error({
          message: 'Có lỗi xảy ra khi lưu thông tin khách hàng'
        });
      }
    }
  };

  // Handle form submit
  const handleSubmit = async () => {
    // Trigger save when form is submitted
    await handleSave();
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('business:customer.detail.generalInfo')}
          </Typography>
          <Chip variant={getStatusVariant(formData.status)} size="sm">
            {getStatusText(formData.status)}
          </Chip>
        </div>
      }
      defaultOpen={true}
    >
      <div>
        <Form ref={formRef} onSubmit={handleSubmit} defaultValues={formData}>
          <div className="space-y-6">
            {/* Avatar và thông tin cơ bản */}
            <div className="flex items-start space-x-4">
              <div className="relative flex flex-col items-center">
                <div className="relative">
                  <Avatar
                    src={formData.avatar || `https://i.pravatar.cc/150?img=${formData.id}`}
                    alt={formData.name}
                    size="3xl"
                    className={`${isUploadingAvatar ? 'opacity-50' : ''} transition-opacity`}
                  />

                  {/* Loading spinner */}
                  {isUploadingAvatar && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                    </div>
                  )}

                </div>

                {/* Upload button - căn giữa với avatar */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAvatarClick}
                  disabled={isUploadingAvatar}
                  className="mt-3"
                >
                  <Icon name="upload" size="sm" className="mr-1" />
                  {isUploadingAvatar ? 'Đang tải...' : 'Thay đổi'}
                </Button>

                {/* Hidden file input */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarUpload}
                  className="hidden"
                />
              </div>

              <div className="flex-1">
                <Typography variant="body2" className="text-muted mb-2">
                  ID: {formData.id}
                </Typography>
              </div>
            </div>

            {/* Form fields */}
            <div className="grid grid-cols-1 gap-4">
              <FormItem label={t('business:common.form.name')} name="name" required>
                <Input
                  value={formData.name}
                  onChange={e => handleInputChange('name', e.target.value)}
                  placeholder={t('business:customer.form.namePlaceholder')}
                  fullWidth
                />
              </FormItem>

              <FormItem label={t('business:common.form.email')} name="email">
                <Input
                  type="email"
                  value={formData.email}
                  onChange={e => handleInputChange('email', e.target.value)}
                  placeholder={t('business:customer.form.emailPlaceholder')}
                  fullWidth
                />
              </FormItem>

              <FormItem label={t('business:common.form.phone')} name="phone">
                <Input
                  value={formData.phone || ''}
                  onChange={e => handleInputChange('phone', e.target.value)}
                  placeholder={t('business:customer.form.phonePlaceholder')}
                  fullWidth
                />
              </FormItem>

              <FormItem label={t('business:common.form.address')} name="address">
                <Input
                  value={formData.address}
                  onChange={e => handleInputChange('address', e.target.value)}
                  placeholder={t('business:customer.form.addressPlaceholder')}
                  fullWidth
                />
              </FormItem>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem label={t('business:customer.detail.customerSince')} name="customerSince">
                  <Input value={formatDate(formData.customerSince)} fullWidth disabled={true} />
                </FormItem>

                <FormItem label={t('business:common.form.tags')} name="tags">
                  <div className="space-y-2">
                    {/* Existing tags */}
                    <div className="flex flex-wrap gap-2">
                      {formData.tags && formData.tags.length > 0 ? (
                        formData.tags.map((tag, index) => (
                          <Chip key={index} size="sm" closable onClose={() => handleRemoveTag(tag)}>
                            {tag}
                          </Chip>
                        ))
                      ) : (
                        <Typography variant="body2" className="text-muted">
                          {t('business:customer.detail.noData')}
                        </Typography>
                      )}
                    </div>

                    {/* Add new tag */}
                    <div className="flex gap-2 items-center">
                      <Input
                        value={newTag}
                        onChange={e => setNewTag(e.target.value)}
                        onKeyDown={e => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleAddTag();
                          }
                        }}
                        placeholder="Thêm tag mới..."
                        className="flex-1"
                        fullWidth
                      />
                    </div>
                  </div>
                </FormItem>
              </div>
            </div>

            {/* Save button */}
            <div className="flex justify-end">
              <IconCard
                icon="check"
                onClick={handleSave}
                disabled={isSaving}
                variant="primary"
                title={t('common:save')}
                size="md"
              />
            </div>
          </div>
        </Form>
      </div>
    </CollapsibleCard>
  );
};

export default CustomerGeneralInfo;
