# New Reply API Examples

Hướng dẫn sử dụng API reply mới với ThreadMessageResponseDto.

## Tổng quan thay đổi

### Trước đây (Legacy)
- Reply content được parse từ string bằng `parseReplyContent`
- Format: `[REPLY:messageId]Original content[/REPLY]Actual message`
- Phải cắt chuỗi để lấy reply info

### Bây giờ (API mới)
- Reply info có sẵn trong response từ API
- Sử dụng `ThreadMessageResponseDto` với `replyToMessageId`
- Không cần parse string

## Cấu trúc API mới

### ThreadMessageResponseDto
```typescript
interface ThreadMessageResponseDto {
  messageId: string;
  threadId: string;
  role: 'user' | 'assistant';
  content: ChatMessageRequest; // Chứa replyToMessageId
  timestamp: number;
  updatedAt?: number;
}
```

### ChatMessageRequest content
```typescript
interface ChatMessageRequest {
  contentBlocks: ChatContentBlock[];
  threadId: string;
  replyToMessageId?: string; // ← Reply info ở đây
  messageId?: string; // ← Modify info ở đây
  alwaysApproveToolCall?: boolean;
  attachmentContext?: ChatAttachmentContext[];
}
```

## Ví dụ Response từ API

### Message thường
```json
{
  "messageId": "msg-123",
  "threadId": "thread-456",
  "role": "user",
  "content": {
    "contentBlocks": [
      { "type": "text", "content": "Hello!" }
    ],
    "threadId": "thread-456"
  },
  "timestamp": 1749811056840
}
```

### Reply message
```json
{
  "messageId": "msg-124",
  "threadId": "thread-456", 
  "role": "user",
  "content": {
    "contentBlocks": [
      { "type": "text", "content": "Thanks for the info!" }
    ],
    "threadId": "thread-456",
    "replyToMessageId": "msg-123"
  },
  "timestamp": 1749811156840
}
```

### Modify message
```json
{
  "messageId": "msg-125",
  "threadId": "thread-456",
  "role": "user", 
  "content": {
    "contentBlocks": [
      { "type": "text", "content": "Corrected message" }
    ],
    "threadId": "thread-456",
    "messageId": "msg-123"
  },
  "timestamp": 1749811256840,
  "updatedAt": 1749811256840
}
```

## Conversion Process

### 1. ThreadMessageResponseDto → ChatMessage
```typescript
function convertThreadMessageToChatMessage(
  threadMessage: ThreadMessageResponseDto,
  allMessages?: ThreadMessageResponseDto[]
): ChatMessage {
  // Lấy text content
  const textContent = threadMessage.content.contentBlocks
    ?.find(block => block.type === 'text')?.content || '';

  // Xử lý reply info
  const replyToMessageId = threadMessage.content.replyToMessageId;
  
  const metadata: ChatMessage['metadata'] = {
    isFromNewAPI: true
  };

  if (replyToMessageId) {
    metadata.replyToMessageId = replyToMessageId;
    
    // Tìm reply content từ allMessages
    if (allMessages) {
      const replyMessage = allMessages.find(msg => msg.messageId === replyToMessageId);
      if (replyMessage) {
        const replyContent = replyMessage.content.contentBlocks
          ?.find(block => block.type === 'text')?.content || '';
        metadata.replyContent = replyContent;
      }
    }
  }

  return {
    id: threadMessage.messageId,
    content: textContent,
    sender: threadMessage.role === 'user' ? 'user' : 'ai',
    timestamp: new Date(threadMessage.timestamp),
    status: MessageStatus.COMPLETED,
    threadId: threadMessage.threadId,
    metadata
  };
}
```

### 2. ChatMessage → UI Props
```typescript
// Trong ChatContent.tsx
const replyInfo = extractReplyInfo(messageData, messages);
if (replyInfo) {
  extraProps['replyInfo'] = replyInfo;
}

// Trong ChatMessage.tsx
const hasReply = !!(replyInfo?.replyToMessageId);
const replyContent = replyInfo?.replyContent;
const replyMessageId = replyInfo?.replyToMessageId;
```

## Utility Functions

### extractReplyInfo
```typescript
function extractReplyInfo(
  message: ChatMessage,
  allMessages?: ChatMessage[]
): { replyToMessageId: string; replyContent?: string } | undefined {
  const replyToMessageId = message.metadata?.replyToMessageId;
  
  if (!replyToMessageId) {
    return undefined;
  }

  // Ưu tiên metadata, fallback tìm từ allMessages
  let replyContent = message.metadata?.replyContent;
  if (!replyContent && allMessages) {
    replyContent = findReplyContent(replyToMessageId, allMessages);
  }

  return { replyToMessageId, replyContent };
}
```

### findReplyContent
```typescript
function findReplyContent(
  replyToMessageId: string,
  messages: ChatMessage[]
): string | undefined {
  const replyMessage = messages.find(msg => msg.id === replyToMessageId);
  return replyMessage ? String(replyMessage.content) : undefined;
}
```

## Migration Checklist

- ✅ Xóa `parseReplyContent` và `replyParser.ts`
- ✅ Thêm `ThreadMessageResponseDto` type
- ✅ Cập nhật `convertThreadMessageToChatMessage`
- ✅ Thêm `extractReplyInfo` utility
- ✅ Cập nhật `ChatMessage.tsx` để sử dụng `replyInfo` prop
- ✅ Cập nhật `ChatContent.tsx` để truyền `replyInfo`
- ✅ Cập nhật `useChatStream` để sử dụng converter mới
- ✅ Cập nhật `GetMessagesResponseData` để sử dụng `ThreadMessageResponseDto[]`

## Benefits

1. **Cleaner Code**: Không cần parse string phức tạp
2. **Better Performance**: Không cần regex operations
3. **Type Safety**: Full TypeScript support
4. **Consistency**: Cùng structure cho input/output
5. **Extensibility**: Dễ thêm features mới (attachments, modify, etc.)
6. **Reliability**: Không phụ thuộc vào string format
