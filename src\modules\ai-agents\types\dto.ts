/**
 * DTO Types cho AI Agents module
 * File này chứa các interface cho Data Transfer Objects
 * Tuân thủ quy tắc ProductGuide.md - Section 3: TYPESCRIPT
 */

import { ConversionField, Module, Resource, Vector } from './agent.types';
import { BaseModelSortByEnum, TypeProviderEnum } from './enums';
import { SortDirection } from './query';

/**
 * Interface cho cấu hình Type Agent
 */
export interface TypeAgentConfig {
  hasProfile?: boolean;
  hasOutput?: boolean;
  hasConversion?: boolean;
  hasResources?: boolean;
  hasStrategy?: boolean;
  hasMultiAgent?: boolean;
}

/**
 * Interface cho Model Config (chỉ chứa parameters, không chứa model selection)
 * ✅ ĐÚNG: Specific types thay vì any
 */
export interface ModelConfigDto {
  temperature?: number;
  top_p?: number;
  top_k?: number;
  max_tokens?: number;
}

/**
 * Interface cho Profile
 * ✅ ĐÚNG: Union types cho specific values
 */
export interface ProfileDto {
  name?: string;
  avatar?: string;
  gender?: 'male' | 'female' | 'other';
  dateOfBirth?: string; // ISO date string
  position?: string;
  education?: string;
  skills?: string[];
  personality?: string[];
  languages?: string[];
  country?: string;
}

/**
 * Interface cho Vector Store
 */
export interface VectorStoreDto {
  id: string;
  name: string;
}

/**
 * Interface cho Group Tool
 */
export interface ToolDto {
  id: number;
  name: string;
  description: string | null;
}

/**
 * Interface cho Agent List Item
 */
export interface AgentListItemDto {
  id: string;
  name: string;
  avatar: string;
  typeId: number;
  typeName: string;
  exp: string;
  expMax: string;
  level: number;
  badgeUrl: string;
  modelId: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho Agent Detail
 */
export interface AgentDetailDto extends AgentListItemDto {
  modelConfig: ModelConfigDto;
  provider_type?: TypeProviderEnum;
  instruction: string;
  profile: ProfileDto;
  vectorStores?: VectorStoreDto;
}

/**
 * Interface cho Type Agent List Item
 */
export interface TypeAgentListItemDto {
  id: number;
  name: string;
  description: string | null;
  config: TypeAgentConfig;
}

/**
 * Interface cho Type Agent Detail
 */
export interface TypeAgentDetailDto extends TypeAgentListItemDto {
  updatedAt: number;
  createdAt: number;
}

/**
 * Interface cho Base Model List Item
 */
export interface BaseModelListItemDto {
  id: string;
  model_id: string;
  description: string;
  providerType: TypeProviderEnum;
  config: {
    // Old format
    top_p?: boolean;
    top_k?: boolean;
    function?: boolean;
    file_search?: boolean;
    temperature?: boolean;
    response_format?: string[];
    code_interpreter?: boolean;
    reasoning_effort?: string[];

    // New format
    hasText?: boolean;
    hasTopK?: boolean;
    hasTopP?: boolean;
    hasAudio?: boolean;
    hasImage?: boolean;
    hasVideo?: boolean;
    hasFunction?: boolean;
    hasTemperature?: boolean;
    hasReasoningEffort?: string[];
    hasParallelToolCall?: boolean;
  };
  createdAt: number;
}

/**
 * Interface cho query params của Base Models
 */
export interface GetBaseModelsQueryDto {
  page?: number;
  limit?: number;
  provider_type?: TypeProviderEnum;
  sortBy?: BaseModelSortByEnum;
  sortDirection?: SortDirection;
}

/**
 * Interface cho tạo Agent (Legacy)
 */
export interface CreateAgentDto {
  name: string;
  typeId: number;
  instruction?: string;
  modelConfig?: Partial<ModelConfigDto>;
  profile?: Partial<ProfileDto>;
  vectorStoreId?: string;
  strategyId?: string;
  mediaIds?: string[];
  productIds?: string[];
  urlIds?: string[];
}

/**
 * Interface cho Profile Modular (validation lỏng hơn)
 */
export interface ProfileModularDto {
  gender?: string;
  dateOfBirth?: number | null | undefined; // timestamp millis
  position?: string;
  education?: string;
  skills?: string[];
  personality?: string[];
  languages?: string[];
  nations?: string | null | undefined;
}

/**
 * Interface cho Output Block
 */
export interface OutputBlockDto {
  facebookPageIds?: string[]; // UUID Facebook Pages
  userWebsiteIds?: string[];  // UUID User Websites
}

/**
 * Interface cho Resources Block
 */
export interface ResourcesBlockDto {
  urlIds?: string[];     // ID URL
  mediaIds?: string[];   // ID media
  productIds?: string[]; // ID product
}

/**
 * Interface cho Strategy Block
 */
export interface StrategyBlockDto {
  strategyId?: string; // ID strategy
}

/**
 * Interface cho Multi Agent Block
 */
export interface MultiAgentBlockDto {
  isEnabled?: boolean;
  coordinatorId?: string;
  subAgentIds?: string[];
}

/**
 * Interface cho Conversion Field
 */
export interface ConversionFieldDto {
  name: string;
  type: string;
  description: string;
  required: boolean;
  active: boolean;
}

/**
 * Interface cho tạo Agent Modular (Recommended)
 * Tuân thủ cấu trúc từ AGENT_USER_CREATION_GUIDE.md
 */
export interface CreateAgentModularDto {
  name: string;                    // Bắt buộc, tối đa 255 ký tự
  typeId: number;                  // Bắt buộc, ID loại agent
  avatarMimeType?: string;         // Tùy chọn, MIME type của avatar
  modelConfig?: ModelConfigDto;    // Tùy chọn, cấu hình model parameters (chỉ khi advanced)
  instruction?: string;            // Tùy chọn, hướng dẫn cho agent
  vectorStoreId?: string;          // Tùy chọn, vector store ID

  // Model Selection Logic mới
  userModelId?: string | null;     // ID của user model (null nếu chọn RedAI)
  keyLlmId?: string | null;        // ID của key LLM (null nếu chọn RedAI)
  modelFineTuneId?: string | null; // ID của fine-tune model (chưa implement)
  systemModelId?: string | null;   // ID của system model (null nếu không chọn RedAI)

  // Các khối modular (tùy thuộc vào TypeAgent config)
  profile?: ProfileModularDto;     // Tùy chọn, thông tin profile
  conversion?: ConversionFieldDto[]; // Tùy chọn, conversion fields
  outputMessenger?: {              // Tùy chọn, messenger output
    facebookPageIds: string[];
  };
  outputWebsite?: {                // Tùy chọn, website output
    userWebsiteIds: string[];
  };
  resources?: ResourcesBlockDto;   // Tùy chọn, tài nguyên
  strategy?: StrategyBlockDto;     // Tùy chọn, chiến lược
  multiAgent?: {                   // Tùy chọn, multi-agent
    multiAgent: Array<{
      agent_id: string;
      prompt: string;
    }>;
  };
  customToolIds?: string[];        // Tùy chọn, custom tool IDs
}

/**
 * Interface cho cập nhật Agent
 */
export interface UpdateAgentDto {
  name?: string;
  instruction?: string;
  modelConfig?: Partial<ModelConfigDto>;
  profile?: Partial<ProfileDto>;
  vectorStoreId?: string;
  // Thêm các field bị thiếu để khắc phục lỗi TypeScript
  conversionFields?: ConversionField[];
  modules?: Module[];
  resources?: Resource[];
  vector?: Vector | null | undefined;
}

/**
 * Interface cho Base Model User Response
 */
export interface BaseModelUserResponseDto {
  id: string;
  model_id: string;
  description: string;
  providerType: TypeProviderEnum;
  config: {
    // Old format
    top_p?: boolean;
    top_k?: boolean;
    function?: boolean;
    file_search?: boolean;
    temperature?: boolean;
    response_format?: string[];
    code_interpreter?: boolean;
    reasoning_effort?: string[];

    // New format
    hasText?: boolean;
    hasTopK?: boolean;
    hasTopP?: boolean;
    hasAudio?: boolean;
    hasImage?: boolean;
    hasVideo?: boolean;
    hasFunction?: boolean;
    hasTemperature?: boolean;
    hasReasoningEffort?: string[];
    hasParallelToolCall?: boolean;
  };
  createdAt: number;
}

/**
 * Interface cho User Provider Model Response
 */
export interface UserProviderModelResponseDto {
  id: string;
  name: string;
  type: TypeProviderEnum;
  createdAt: number;
}

/**
 * Interface cho tạo Type Agent
 */
export interface CreateTypeAgentDto {
  name: string;
  description?: string;
  config: TypeAgentConfig;
  groupToolIds: number[];
}

/**
 * Interface cho cập nhật Type Agent
 */
export interface UpdateTypeAgentDto {
  name?: string;
  description?: string;
  config?: TypeAgentConfig;
  groupToolIds?: number[];
}



/**
 * Response cho cập nhật Vector Store
 */
export interface UpdateAgentVectorStoreDto {
  vectorStoreId: string;
}
