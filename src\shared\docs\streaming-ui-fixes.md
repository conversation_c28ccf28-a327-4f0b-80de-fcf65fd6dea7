# Streaming UI Fixes - Reply Indicator & Message Splitting

Tài liệu này mô tả các fix cho 2 vấn đề chính trong streaming UI.

## ❌ Vấn đề đã phát hiện

### 1. UI không hiển thị reply indicator
- Message được gửi với `replyToMessageId` đúng
- Nhưng UI không hiển thị được reply vào message nào
- Thiếu reply metadata trong streaming messages

### 2. Stream bị cắt thành 2 tin nhắn
- Một message bị chia thành 2 messages riêng biệt
- Vẫn có cursor sau khi stream hoàn thành
- Role change logic gây ra message splitting

## ✅ Giải pháp đã implement

### Fix 1: Reply Metadata trong User Message

**Vấn đề**: User message không có reply metadata
```typescript
// ❌ Trước đây - thiếu reply info
const userMessage: ChatMessage = {
  id: uuidv4(),
  content: messageContent,
  sender: 'user',
  timestamp: new Date(),
  status: MessageStatus.SENT,
  threadId: currentThreadId
};
```

**Gi<PERSON>i pháp**: Thêm reply metadata từ request
```typescript
// ✅ Sau khi sửa - có reply info
const replyToMessageId = !isLegacyRequest ? (contentOrRequest as ChatMessageRequest).replyToMessageId : undefined;

const userMessage: ChatMessage = {
  id: uuidv4(),
  content: messageContent,
  sender: 'user',
  timestamp: new Date(),
  status: MessageStatus.SENT,
  threadId: currentThreadId,
  metadata: {
    ...(replyToMessageId && { replyToMessageId })
  }
};
```

### Fix 2: Enhanced Debug Logging cho Role Changes

**Vấn đề**: Không biết khi nào role change xảy ra
```typescript
// ❌ Trước đây - không có debug info
} else if (currentStreamingMessageRef.current.sender !== mappedSender) {
  // Finalize message hiện tại và tạo message mới
```

**Giải pháp**: Thêm debug logs chi tiết
```typescript
// ✅ Sau khi sửa - có debug logs
} else if (currentStreamingMessageRef.current.sender !== mappedSender) {
  console.warn('[useChatStream] 🔄 ROLE CHANGE DETECTED - Creating new message:', {
    currentSender: currentStreamingMessageRef.current.sender,
    newSender: mappedSender,
    currentRole: currentRole,
    newRole: role,
    text: text.substring(0, 50) + '...'
  });
  // Finalize và tạo message mới
```

### Fix 3: Proper Stream Session End Handling

**Vấn đề**: Cursor không biến mất sau khi stream hoàn thành
```typescript
// ❌ Trước đây - không finalize message
onStreamSessionEnd: () => {
  setIsStreaming(false);
  setIsLoading(false);
  setCurrentStreamingText('');
  // ... other cleanup
},
```

**Giải pháp**: Finalize message trước khi cleanup
```typescript
// ✅ Sau khi sửa - finalize message trước
onStreamSessionEnd: () => {
  console.log('[useChatStream] 🏁 STREAM SESSION END - Cleaning up all streaming state');
  
  // Finalize current streaming message if exists
  if (currentStreamingMessageRef.current) {
    const finalMessage: ChatMessage = {
      ...currentStreamingMessageRef.current,
      status: MessageStatus.COMPLETED,
      timestamp: new Date()
    };

    setMessages(prev => prev.map(msg =>
      msg.id === currentStreamingMessageRef.current?.id ? finalMessage : msg
    ));
  }

  // Then cleanup state
  setIsStreaming(false);
  setCurrentStreamingText(''); // ✅ Clear cursor
  // ... other cleanup
},
```

### Fix 4: Enhanced LLM Stream End Handling

**Vấn đề**: Message không được finalize đúng cách
```typescript
// ❌ Trước đây - chỉ handle supervisor
if (role === 'supervisor') {
  // Finalize message
}
```

**Giải pháp**: Handle cả supervisor và assistant
```typescript
// ✅ Sau khi sửa - handle nhiều roles
if (role === 'supervisor' || role === 'assistant') {
  const finalMessage: ChatMessage = {
    ...currentStreamingMessageRef.current,
    status: MessageStatus.COMPLETED,
    timestamp: new Date(),
    metadata: {
      ...currentStreamingMessageRef.current.metadata,
      processingTime: Date.now() - currentStreamingMessageRef.current.timestamp.getTime()
    }
  };
  // Update UI
}
```

## 🔍 Debug Flow

### Expected Log Sequence

1. **Send Reply Message**:
   ```
   [ChatInputBox] 📤 Sending reply message: {content: "...", replyToMessageId: "...", threadId: "..."}
   ```

2. **User Message Created**:
   ```
   [useChatStream] User message created with reply metadata: {replyToMessageId: "..."}
   ```

3. **Stream Tokens**:
   ```
   [useChatStream] 🔤 TEXT TOKEN RECEIVED: {text: "...", role: "assistant"}
   ```

4. **Role Change Detection** (if happens):
   ```
   [useChatStream] 🔄 ROLE CHANGE DETECTED - Creating new message: {currentSender: "...", newSender: "..."}
   ```

5. **Stream End**:
   ```
   [useChatStream] 🏁 LLM Stream End for role: assistant
   [useChatStream] ✅ Message finalized for role: assistant
   [useChatStream] 🏁 STREAM SESSION END - Cleaning up all streaming state
   [useChatStream] ✅ Stream session cleanup completed
   ```

## 🎯 Expected Results

### 1. Reply Indicator Display
- User message hiển thị với reply indicator
- AI response hiển thị với reply context
- Click reply indicator focus vào original message

### 2. Single Continuous Message
- AI response không bị cắt thành nhiều messages
- Smooth streaming từ đầu đến cuối
- Cursor biến mất sau khi hoàn thành

### 3. Proper State Management
- `isStreaming` = false sau khi hoàn thành
- `currentStreamingText` = '' (no cursor)
- Message status = COMPLETED

## 🔧 Troubleshooting

### Issue 1: Reply Indicator vẫn không hiển thị
- Check `replyToMessageId` trong user message metadata
- Check `extractReplyInfo` function trong ChatContent
- Check `replyInfo` prop được truyền vào ChatMessage

### Issue 2: Message vẫn bị split
- Check console logs cho "ROLE CHANGE DETECTED"
- Xem role nào đang thay đổi và tại sao
- Check `mapRoleToMessageSender` function

### Issue 3: Cursor vẫn còn
- Check `onStreamSessionEnd` có được gọi không
- Check `setCurrentStreamingText('')` có được execute không
- Check message có được finalize với status COMPLETED không

## 🚀 Testing

### Test Cases
1. **Reply Message**: Gửi reply và check UI hiển thị
2. **Long Response**: Test streaming dài để check splitting
3. **Multiple Roles**: Test response có nhiều roles
4. **Stream Completion**: Check cursor biến mất

### Expected Behavior
- Reply indicator hiển thị đúng
- Single continuous message
- Clean stream completion
- No lingering cursors

Với những fixes này, streaming UI sẽ hoạt động smooth và hiển thị reply context đúng cách!
