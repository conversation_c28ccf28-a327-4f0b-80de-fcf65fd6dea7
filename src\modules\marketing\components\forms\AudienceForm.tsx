import React, { useState } from 'react';
import { Card, Form, FormItem, Input, IconCard, Typography, TagsSelectWithPagination, PhoneInputWithCountry } from '@/shared/components/common';
import { z } from 'zod';

// Schema cho form - chỉ validate các field trong Form component
const formSchema = z.object({
  name: z.string().min(1, 'Tên đối tượng là bắt buộc'),
  email: z
    .string()
    .optional()
    .refine((val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: 'Email không hợp lệ'
    }),
});

export type AudienceFormValues = z.infer<typeof formSchema>;

interface AudienceFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
}

/**
 * Component form thêm/sửa đối tượng
 */
const AudienceForm: React.FC<AudienceFormProps> = ({ onSubmit, onCancel }) => {
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [phoneValue, setPhoneValue] = useState<string>('');
  const [countryCode, setCountryCode] = useState<string>('+84');

  const handlePhoneChange = (phone: string, country: string) => {
    setPhoneValue(phone);
    setCountryCode(country);
  };

  const handleSubmit = (values: Record<string, unknown>) => {
    console.log('AudienceForm handleSubmit called with:', values); // Debug log
    console.log('Phone value:', phoneValue); // Debug log
    console.log('Country code:', countryCode); // Debug log
    console.log('Selected tags:', selectedTags); // Debug log

    // Thêm selectedTags, phone, countryCode và customFields vào values
    const formData = {
      ...values,
      ...(phoneValue && phoneValue.trim() && { phone: phoneValue }),
      ...(countryCode && { countryCode: countryCode }),
      tagIds: selectedTags,
    };

    console.log('Final form data:', formData); // Debug log
    onSubmit(formData);
  };

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        Thêm đối tượng mới
      </Typography>

      <Form schema={formSchema} onSubmit={handleSubmit} submitOnEnter={false} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="name" label="Tên đối tượng" required>
            <Input placeholder="Nhập tên đối tượng" fullWidth />
          </FormItem>

          <FormItem name="email" label="Email">
            <Input type="email" placeholder="Nhập email" fullWidth />
          </FormItem>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Số điện thoại
            </label>
            <PhoneInputWithCountry
              value={phoneValue}
              onChange={handlePhoneChange}
              placeholder="Nhập số điện thoại"
              fullWidth
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Tags</label>
            <TagsSelectWithPagination
              value={selectedTags}
              onChange={setSelectedTags}
              placeholder="Chọn tags..."
              fullWidth
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <IconCard
            icon="x"
            variant="default"
            size="md"
            title="Hủy"
            onClick={onCancel}
          />
          <IconCard
            icon="save"
            variant="default"
            size="md"
            title="Lưu"
            onClick={() => {
              // Trigger form submit programmatically
              const form = document.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }}
          />
        </div>
      </Form>
    </Card>
  );
};

export default AudienceForm;
