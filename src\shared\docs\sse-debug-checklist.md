# SSE Debug Checklist

Checklist để debug SSE streaming không nhận được tokens.

## ✅ Đã sửa

1. **Missing onCharacterStreamed callback**: ✅ Đã thêm callback trong SSE service
2. **Missing setCurrentMessageId**: ✅ Đã thêm trong useChatStream
3. **Debug mode**: ✅ Đã enable debug mode cho SSE service
4. **Enhanced logging**: ✅ Đã thêm debug logs chi tiết

## 🔍 Debug Flow

### 1. Gửi Message
```
[useChatStream] 📤 Calling API sendMessage...
[useChatStream] ✅ Message sent, response: {runId: "..."}
[useChatStream] 🆔 RunId set: "..."
```

### 2. SSE Connection
```
[useChatStream] 🔌 Connecting SSE...
[ChatSSEService] SSE URL: {...}
[ChatSSEService] Creating EventSource with URL: {...}
[ChatSSEService] SSE connection opened successfully
[useChatStream] ✅ SSE connected successfully and messageId set: "..."
```

### 3. Token Reception
```
[SSE] 🔥 RAW MESSAGE RECEIVED: {data: "...", type: "message"}
[SSE] 🎯 Routing event: "stream_text_token"
[SSE] 📝 Routing to text token handler
[SSE] 🚀 Processing text token: {text: "...", role: "...", useQueue: true}
[SSE] ✅ Token queued successfully
```

### 4. Queue Processing
```
[SSE] 🔤 Character streamed from queue: {character: "...", role: "..."}
[useChatStream] 🔤 TEXT TOKEN RECEIVED: {text: "...", role: "...", isStreaming: true, isLoading: true}
```

## 🚨 Potential Issues

### Issue 1: State Check Failure
```javascript
// Trong useChatStream onTextToken callback
if (!isStreaming && !isLoading) {
  console.warn('[useChatStream] ⚠️ IGNORING TOKEN - not in streaming/loading state');
  return;
}
```

**Solution**: Đảm bảo `isLoading = true` khi gửi message và `isStreaming = true` khi nhận token đầu tiên.

### Issue 2: Missing currentMessageId
```javascript
// Trong SSE service
if (this.useQueueMode && this.streamingController && this.currentMessageId) {
  // Queue tokens
} else {
  // Fallback to direct streaming
}
```

**Solution**: ✅ Đã thêm `setCurrentMessageId(runId)` sau khi connect SSE.

### Issue 3: StreamingController Not Processing
```javascript
// StreamingController callbacks
onCharacterStreamed: (item) => {
  this.callbacks.onTextToken?.(item.character, item.role || 'assistant');
}
```

**Solution**: ✅ Đã thêm callback này.

## 🔧 Debug Commands

### 1. Check SSE Connection Status
```javascript
// Trong browser console
window.sseService = sseServiceRef.current;
console.log(window.sseService?.getConnectionStatus());
```

### 2. Check StreamingController State
```javascript
// Trong browser console
const controller = window.sseService?.getStreamingController();
console.log(controller?.getCurrentState());
```

### 3. Manual Token Test
```javascript
// Test thêm token manually
const controller = window.sseService?.getStreamingController();
controller?.addToken({
  text: 'Test token',
  role: 'assistant',
  messageId: 'test-msg',
  timestamp: Date.now(),
  priority: 1,
  metadata: {}
});
```

## 📋 Checklist khi Debug

- [ ] Kiểm tra console logs cho SSE connection
- [ ] Kiểm tra `isLoading` và `isStreaming` state
- [ ] Kiểm tra `currentMessageId` đã được set chưa
- [ ] Kiểm tra tokens có được queue không
- [ ] Kiểm tra character stream có được emit không
- [ ] Kiểm tra onTextToken callback có được gọi không

## 🎯 Expected Log Sequence

1. **Send Message**:
   - `📤 Calling API sendMessage...`
   - `✅ Message sent, response:`
   - `🆔 RunId set:`

2. **SSE Connect**:
   - `🔌 Connecting SSE...`
   - `SSE connection opened successfully`
   - `✅ SSE connected successfully and messageId set:`

3. **Receive Tokens**:
   - `🔥 RAW MESSAGE RECEIVED:`
   - `🎯 Routing event: "stream_text_token"`
   - `🚀 Processing text token:`
   - `✅ Token queued successfully`

4. **Stream Characters**:
   - `🔤 Character streamed from queue:`
   - `🔤 TEXT TOKEN RECEIVED:`

Nếu sequence bị break ở bước nào, đó là nơi cần debug tiếp.

## 🔍 Common Issues

1. **No tokens received**: Kiểm tra SSE URL và connection
2. **Tokens ignored**: Kiểm tra `isLoading`/`isStreaming` state
3. **Queue not processing**: Kiểm tra `currentMessageId` và StreamingController
4. **Characters not streaming**: Kiểm tra `onCharacterStreamed` callback
