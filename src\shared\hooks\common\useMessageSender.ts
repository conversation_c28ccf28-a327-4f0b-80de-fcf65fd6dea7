/**
 * Hook helper để gửi tin nhắn với API mới
 * Cung cấp các method tiện lợi cho việc gửi tin nhắn
 */

import { useCallback } from 'react';
import { useChatStream } from './useChatStream';
import {
  ChatMessageRequest,
  ChatMessageResponse
} from '@/shared/types/chat-streaming.types';
import {
  createTextMessage,
  createReplyMessage,
  createModifyMessage,
  createMessageWithAttachments,
  validateMessageRequest
} from '@/shared/utils/message-request.utils';

export interface UseMessageSenderReturn {
  /**
   * Gửi tin nhắn text đơn giản
   */
  sendText: (content: string, options?: {
    threadId?: string;
    alwaysApproveToolCall?: boolean;
  }) => Promise<ChatMessageResponse | null>;

  /**
   * Reply tin nhắn
   */
  replyTo: (
    content: string,
    replyToMessageId: string,
    options?: {
      threadId?: string;
      alwaysApproveToolCall?: boolean;
    }
  ) => Promise<ChatMessageResponse | null>;

  /**
   * Modify tin nhắn hiện có
   */
  modifyMessage: (
    content: string,
    messageId: string,
    options?: {
      threadId?: string;
      alwaysApproveToolCall?: boolean;
    }
  ) => Promise<ChatMessageResponse | null>;

  /**
   * Gửi tin nhắn với attachments
   */
  sendWithAttachments: (
    content: string,
    attachments: Array<{
      type: 'file' | 'image';
      id: string;
    }>,
    options?: {
      threadId?: string;
      replyToMessageId?: string;
      messageId?: string;
      alwaysApproveToolCall?: boolean;
    }
  ) => Promise<ChatMessageResponse | null>;

  /**
   * Approve tool call
   */
  approveToolCall: (
    decision: 'yes' | 'no' | 'always',
    options?: {
      threadId?: string;
    }
  ) => Promise<ChatMessageResponse | null>;

  /**
   * Gửi ChatMessageRequest tùy chỉnh
   */
  sendCustom: (request: ChatMessageRequest) => Promise<ChatMessageResponse | null>;

  /**
   * Validate ChatMessageRequest trước khi gửi
   */
  validateRequest: (request: ChatMessageRequest) => {
    isValid: boolean;
    errors: string[];
  };

  // Expose các properties từ useChatStream
  threadId: string | null;
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;
  streamError: {
    message: string;
    details?: unknown;
    retryContent?: string;
  } | null;
}

/**
 * Hook để gửi tin nhắn với API mới
 */
export function useMessageSender(chatStream: ReturnType<typeof useChatStream>): UseMessageSenderReturn {
  const {
    sendMessage,
    threadId,
    isLoading,
    isStreaming,
    error,
    streamError
  } = chatStream;

  /**
   * Gửi tin nhắn text đơn giản
   */
  const sendText = useCallback(async (
    content: string,
    options?: {
      threadId?: string;
      alwaysApproveToolCall?: boolean;
    }
  ) => {
    const targetThreadId = options?.threadId || threadId;
    if (!targetThreadId) {
      throw new Error('Thread ID is required');
    }

    const request = createTextMessage(content, targetThreadId, {
      alwaysApproveToolCall: options?.alwaysApproveToolCall
    });

    return sendMessage(request);
  }, [sendMessage, threadId]);

  /**
   * Reply tin nhắn
   */
  const replyTo = useCallback(async (
    content: string,
    replyToMessageId: string,
    options?: {
      threadId?: string;
      alwaysApproveToolCall?: boolean;
    }
  ) => {
    const targetThreadId = options?.threadId || threadId;
    if (!targetThreadId) {
      throw new Error('Thread ID is required');
    }

    const request = createReplyMessage(content, targetThreadId, replyToMessageId, {
      alwaysApproveToolCall: options?.alwaysApproveToolCall
    });

    return sendMessage(request);
  }, [sendMessage, threadId]);

  /**
   * Modify tin nhắn hiện có
   */
  const modifyMessage = useCallback(async (
    content: string,
    messageId: string,
    options?: {
      threadId?: string;
      alwaysApproveToolCall?: boolean;
    }
  ) => {
    const targetThreadId = options?.threadId || threadId;
    if (!targetThreadId) {
      throw new Error('Thread ID is required');
    }

    const request = createModifyMessage(content, targetThreadId, messageId, {
      alwaysApproveToolCall: options?.alwaysApproveToolCall
    });

    return sendMessage(request);
  }, [sendMessage, threadId]);

  /**
   * Gửi tin nhắn với attachments
   */
  const sendWithAttachments = useCallback(async (
    content: string,
    attachments: Array<{
      type: 'file' | 'image';
      id: string;
    }>,
    options?: {
      threadId?: string;
      replyToMessageId?: string;
      messageId?: string;
      alwaysApproveToolCall?: boolean;
    }
  ) => {
    const targetThreadId = options?.threadId || threadId;
    if (!targetThreadId) {
      throw new Error('Thread ID is required');
    }

    const request = createMessageWithAttachments(content, targetThreadId, attachments, {
      replyToMessageId: options?.replyToMessageId,
      messageId: options?.messageId,
      alwaysApproveToolCall: options?.alwaysApproveToolCall
    });

    return sendMessage(request);
  }, [sendMessage, threadId]);

  /**
   * Approve tool call
   */
  const approveToolCall = useCallback(async (
    decision: 'yes' | 'no' | 'always',
    options?: {
      threadId?: string;
    }
  ) => {
    const targetThreadId = options?.threadId || threadId;
    if (!targetThreadId) {
      throw new Error('Thread ID is required');
    }

    const request: ChatMessageRequest = {
      contentBlocks: [{ type: 'tool_call_decision', decision }],
      threadId: targetThreadId,
      alwaysApproveToolCall: decision === 'always'
    };

    return sendMessage(request);
  }, [sendMessage, threadId]);

  /**
   * Gửi ChatMessageRequest tùy chỉnh
   */
  const sendCustom = useCallback(async (request: ChatMessageRequest) => {
    // Validate request trước khi gửi
    const validation = validateMessageRequest(request);
    if (!validation.isValid) {
      throw new Error(`Invalid ChatMessageRequest: ${validation.errors.join(', ')}`);
    }

    return sendMessage(request);
  }, [sendMessage]);

  /**
   * Validate ChatMessageRequest
   */
  const validateRequest = useCallback((request: ChatMessageRequest) => {
    return validateMessageRequest(request);
  }, []);

  return {
    sendText,
    replyTo,
    modifyMessage,
    sendWithAttachments,
    approveToolCall,
    sendCustom,
    validateRequest,
    threadId,
    isLoading,
    isStreaming,
    error,
    streamError
  };
}
