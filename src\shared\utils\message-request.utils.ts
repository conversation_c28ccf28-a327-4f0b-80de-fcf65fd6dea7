/**
 * Utility functions để tạo MessageRequest dễ dàng hơn
 */

import {
  MessageRequest,
  MessageContentBlock,
  MessageAttachmentContext
} from '@/shared/types/chat-streaming.types';

/**
 * Tạo MessageRequest cho tin nhắn text đơn giản
 */
export function createTextMessage(
  content: string,
  threadId: string,
  options?: {
    alwaysApproveToolCall?: boolean;
  }
): MessageRequest {
  return {
    contentBlocks: [{ type: 'text', content }],
    threadId,
    alwaysApproveToolCall: options?.alwaysApproveToolCall
  };
}

/**
 * Tạo MessageRequest cho reply message
 */
export function createReplyMessage(
  content: string,
  threadId: string,
  replyToMessageId: string,
  options?: {
    alwaysApproveToolCall?: boolean;
  }
): MessageRequest {
  return {
    contentBlocks: [{ type: 'text', content }],
    threadId,
    replyToMessageId,
    alwaysApproveToolCall: options?.alwaysApproveToolCall
  };
}

/**
 * Tạo MessageRequest cho modify message
 */
export function createModifyMessage(
  content: string,
  threadId: string,
  messageId: string,
  options?: {
    alwaysApproveToolCall?: boolean;
  }
): MessageRequest {
  return {
    contentBlocks: [{ type: 'text', content }],
    threadId,
    messageId,
    alwaysApproveToolCall: options?.alwaysApproveToolCall
  };
}

/**
 * Tạo MessageRequest với attachments
 */
export function createMessageWithAttachments(
  content: string,
  threadId: string,
  attachments: Array<{
    type: 'file' | 'image';
    id: string;
  }>,
  options?: {
    replyToMessageId?: string;
    messageId?: string;
    alwaysApproveToolCall?: boolean;
  }
): MessageRequest {
  const contentBlocks: MessageContentBlock[] = [
    { type: 'text', content }
  ];

  const attachmentContext: MessageAttachmentContext[] = [];

  // Thêm attachment blocks và context
  attachments.forEach(attachment => {
    if (attachment.type === 'file') {
      contentBlocks.push({ type: 'file', fileId: attachment.id });
      attachmentContext.push({ type: 'file', fileId: attachment.id });
    } else if (attachment.type === 'image') {
      contentBlocks.push({ type: 'image', imageId: attachment.id });
      attachmentContext.push({ type: 'image', imageId: attachment.id });
    }
  });

  return {
    contentBlocks,
    threadId,
    attachmentContext,
    replyToMessageId: options?.replyToMessageId,
    messageId: options?.messageId,
    alwaysApproveToolCall: options?.alwaysApproveToolCall
  };
}

/**
 * Tạo MessageRequest từ content blocks tùy chỉnh
 */
export function createCustomMessage(
  contentBlocks: MessageContentBlock[],
  threadId: string,
  options?: {
    replyToMessageId?: string;
    messageId?: string;
    alwaysApproveToolCall?: boolean;
    attachmentContext?: MessageAttachmentContext[];
  }
): MessageRequest {
  return {
    contentBlocks,
    threadId,
    replyToMessageId: options?.replyToMessageId,
    messageId: options?.messageId,
    alwaysApproveToolCall: options?.alwaysApproveToolCall,
    attachmentContext: options?.attachmentContext
  };
}

/**
 * Kiểm tra xem MessageRequest có phải là reply không
 */
export function isReplyMessage(request: MessageRequest): boolean {
  return !!request.replyToMessageId;
}

/**
 * Kiểm tra xem MessageRequest có phải là modify không
 */
export function isModifyMessage(request: MessageRequest): boolean {
  return !!request.messageId;
}

/**
 * Kiểm tra xem MessageRequest có attachments không
 */
export function hasAttachments(request: MessageRequest): boolean {
  return !!request.attachmentContext && request.attachmentContext.length > 0;
}

/**
 * Lấy text content từ MessageRequest
 */
export function getTextContent(request: MessageRequest): string {
  const textBlock = request.contentBlocks.find(block => block.type === 'text');
  return textBlock?.content || '';
}

/**
 * Lấy danh sách file IDs từ MessageRequest
 */
export function getFileIds(request: MessageRequest): string[] {
  return request.contentBlocks
    .filter(block => block.type === 'file' && block.fileId)
    .map(block => block.fileId!);
}

/**
 * Lấy danh sách image IDs từ MessageRequest
 */
export function getImageIds(request: MessageRequest): string[] {
  return request.contentBlocks
    .filter(block => block.type === 'image' && block.imageId)
    .map(block => block.imageId!);
}

/**
 * Validate MessageRequest
 */
export function validateMessageRequest(request: MessageRequest): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!request.threadId) {
    errors.push('threadId is required');
  }

  if (!request.contentBlocks || request.contentBlocks.length === 0) {
    errors.push('contentBlocks cannot be empty');
  }

  if (request.replyToMessageId && request.messageId) {
    errors.push('Cannot have both replyToMessageId and messageId');
  }

  // Validate content blocks
  request.contentBlocks.forEach((block, index) => {
    if (!block.type) {
      errors.push(`contentBlocks[${index}]: type is required`);
    }

    if (block.type === 'text' && !block.content) {
      errors.push(`contentBlocks[${index}]: content is required for text blocks`);
    }

    if (block.type === 'file' && !block.fileId) {
      errors.push(`contentBlocks[${index}]: fileId is required for file blocks`);
    }

    if (block.type === 'image' && !block.imageId) {
      errors.push(`contentBlocks[${index}]: imageId is required for image blocks`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Clone MessageRequest với modifications
 */
export function cloneMessageRequest(
  request: MessageRequest,
  modifications?: Partial<MessageRequest>
): MessageRequest {
  return {
    ...request,
    contentBlocks: [...request.contentBlocks],
    attachmentContext: request.attachmentContext ? [...request.attachmentContext] : undefined,
    ...modifications
  };
}
