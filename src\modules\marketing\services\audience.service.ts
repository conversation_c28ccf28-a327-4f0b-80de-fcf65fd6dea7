/**
 * Service for audience API
 */

import { apiClient } from '@/shared/api';
import {
  AudienceDetailResponse,
  AudienceListResponse,
  AudienceQueryParams,
  CreateAudienceRequest,
  UpdateAudienceRequest,
  BulkUpdateCustomFieldsDto,
} from '../types/audience.types';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { MarketingCustomFieldResponse } from '../types/custom-field.types';

/**
 * Base URL for audience API
 */
const BASE_URL = '/marketing/audiences';

/**
 * Audience service
 */
export const AudienceService = {
  /**
   * Get audiences with pagination and filtering
   */
  getAudiences: async (params?: AudienceQueryParams): Promise<AudienceListResponse> => {
    console.log('AudienceService.getAudiences called with params:', params); // Debug log
    console.log('API endpoint:', BASE_URL); // Debug log
    try {
      const result = await apiClient.get<AudienceListResponse['result']>(BASE_URL, { params });
      console.log('API response:', result); // Debug log
      return result;
    } catch (error) {
      console.error('API error:', error); // Debug log
      throw error;
    }
  },

  /**
   * Get audience by ID
   */
  getAudienceById: async (id: number): Promise<AudienceDetailResponse> => {
    return apiClient.get<AudienceDetailResponse['result']>(`${BASE_URL}/${id}`);
  },

  /**
   * Create audience
   */
  createAudience: async (data: CreateAudienceRequest): Promise<AudienceDetailResponse> => {
    console.log('AudienceService.createAudience called with:', data); // Debug log
    console.log('API endpoint:', BASE_URL); // Debug log
    try {
      const result = await apiClient.post<AudienceDetailResponse['result']>(BASE_URL, data);
      console.log('API response:', result); // Debug log
      return result;
    } catch (error) {
      console.error('API error:', error); // Debug log
      throw error;
    }
  },

  /**
   * Update audience
   */
  updateAudience: async (
    id: number,
    data: UpdateAudienceRequest
  ): Promise<AudienceDetailResponse> => {
    return apiClient.put<AudienceDetailResponse['result']>(`${BASE_URL}/${id}`, data);
  },

  /**
   * Delete audience
   */
  deleteAudience: async (id: number): Promise<ApiResponseDto<{ success: boolean }>> => {
    return apiClient.delete<{ success: boolean }>(`${BASE_URL}/${id}`);
  },

  /**
   * Delete multiple audiences
   */
  deleteMultipleAudiences: async (ids: number[]): Promise<ApiResponseDto<{ success: boolean }>> => {
    return apiClient.delete<{ success: boolean }>(BASE_URL, { data: { ids } });
  },

  /**
   * Bulk update custom fields for audience
   */
  bulkUpdateCustomFields: async (
    audienceId: number,
    data: BulkUpdateCustomFieldsDto
  ): Promise<ApiResponseDto<MarketingCustomFieldResponse[]>> => {
    console.log('AudienceService.bulkUpdateCustomFields called with:', { audienceId, data });
    try {
      const result = await apiClient.put<MarketingCustomFieldResponse[]>(
        `/user/marketing/audiences/${audienceId}/custom-fields`,
        data
      );
      console.log('Bulk update custom fields response:', result);
      return result;
    } catch (error) {
      console.error('Error in bulkUpdateCustomFields:', error);
      throw error;
    }
  },
};
