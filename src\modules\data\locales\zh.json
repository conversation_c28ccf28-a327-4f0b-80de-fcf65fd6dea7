{"data": {"title": "数据管理", "description": "集中管理系统数据，包括媒体、知识文件、网址和工具。", "media": {"title": "媒体库", "description": "管理媒体文件，如图片、视频、音频和文档。", "totalFiles": "文件总数", "manage": "管理媒体", "table": {"name": "文件名", "type": "文件类型", "viewUrl": "网址", "status": "状态", "ownedBy": "拥有者", "size": "大小", "description": "描述", "uploadDate": "上传日期", "actions": "操作", "thumbnail": "缩略图"}, "detail": {"title": "媒体详情", "name": "文件名", "description": "描述", "type": "类型", "tags": "标签", "status": "状态", "ownedBy": "拥有者", "uploadDate": "上传日期", "viewUrl": "查看网址"}, "actions": {"view": "查看", "delete": "删除"}, "upload": {"title": "上传媒体", "button": "上传", "progress": "上传中... {{progress}}%", "success": "上传成功", "error": "上传失败"}, "delete": {"confirm": "确定要删除此文件吗？", "success": "文件删除成功", "error": "文件删除失败"}}, "knowledgeFiles": {"title": "知识文件", "description": "管理用于AI和向量存储的知识文件。", "totalFiles": "文件总数", "manage": "管理知识文件", "dragAndDrop": "拖放文件到此处或点击上传", "supportedFormats": "支持的格式：PDF, DOCX, TXT, CSV, JSON", "selectFilesToUpload": "请选择要上传的文件", "selectedFiles": "已选择的文件", "uploadFiles": "上传文件", "fileName": "文件名", "fileSize": "文件大小", "fileType": "文件类型", "vectorStore": "向量存储", "noVectorStore": "没有向量存储", "createdAt": "创建时间", "table": {"name": "文件名", "extension": "格式", "size": "大小", "view网址": "查看网址", "actions": "操作", "url": "网址"}, "upload": {"title": "上传知识文件", "button": "上传", "progress": "上传中... {{progress}}%", "success": "上传成功", "error": "上传失败"}, "delete": {"confirm": "确定要删除此文件吗？", "success": "文件删除成功", "error": "文件删除失败"}}, "files": {"form": {"files": "文件", "dragAndDrop": "拖放文件到此处或点击上传", "fileDetails": "文件详情", "fileDetailsDescription": "编辑每个文件的信息", "name": "文档名称", "description": "描述", "tags": "标签", "tagsPlaceholder": "输入标签并按回车键", "invalidFile": "无效文件", "nameRequired": "文档名称为必填项", "descriptionRequired": "描述为必填项", "fileRequired": "请至少选择一个文件", "selectedFiles": "已选择的文件", "descriptionPlaceholder": "输入描述"}}, "url": {"crawlUrl": "抓取网址", "title": "网址管理", "description": "管理系统中的网址和网络资源。", "totalUrls": "网址总数", "manage": "管理网址", "viewUrl": "网址详情", "editUrl": "编辑网址", "addNew": "添加新网址", "table": {"url": "网址", "title": "标题", "type": "类型", "tags": "标签", "description": "描述", "createdAt": "创建日期", "actions": "操作"}, "form": {"url": "网址", "urlDescription": "输入完整网址，包括http://或https://", "title": "标题", "description": "描述", "type": "类型", "tags": "标签", "tagsDescription": "输入标签，用逗号分隔", "urlPlaceholder": "https://example.com", "titlePlaceholder": "输入标题", "tagsPlaceholder": "输入标签并按回车键", "ownedByPlaceholder": "选择拥有者", "activeStatus": "启用", "fileDetailsDescription": "编辑每个文件的信息", "descriptionPlaceholder": "输入描述", "typePlaceholder": "选择网址类型", "ownedBy": "拥有者", "isActive": "是否启用", "depth": "抓取深度", "maxUrls": "最大网址数量", "ignoreRobotsTxt": "忽略robots.txt", "startCrawl": "开始抓取"}, "add": {"title": "添加新网址", "button": "添加网址", "success": "网址添加成功", "error": "网址添加失败"}, "delete": {"confirm": "确定要删除此网址吗？", "success": "网址删除成功", "error": "网址删除失败"}}, "vectorStore": {"title": "向量存储", "description": "管理用于AI应用和语义搜索的向量存储和嵌入。", "descriptionDetail": "描述描述", "totalStores": "向量存储总数", "manage": "管理向量存储", "assignFiles": "分配文件", "confirmDeleteMessage": "确定要删除此向量存储吗？", "details": "向量存储详情", "createVectorStore": "创建新向量存储", "viewVectorStore": "查看向量存储", "namePlaceholder": "输入向量存储名称", "createdAt": "创建日期", "form": {"title": "创建新向量存储", "name": "向量存储名称", "assignFiles": "将文件分配到向量存储", "fileIds": "文件ID（用逗号分隔）", "namePlaceholder": "输入向量存储名称"}, "table": {"name": "向量存储名称", "files": "文件数量", "size": "大小", "agents": "代理数量", "createdAt": "创建日期"}}, "common": {"actions": "操作", "createdAt": "创建日期", "cancel": "取消", "save": "保存", "delete": "删除", "confirmDelete": "确认删除", "all": "全部", "search": "搜索...", "noData": "暂无数据", "loading": "加载中...", "error": "发生错误", "upload": "上传", "uploading": "上传中...", "close": "关闭", "copy": "复制", "start": "开始", "create": "创建", "creating": "创建中..."}}}