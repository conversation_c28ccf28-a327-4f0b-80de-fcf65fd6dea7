# Message API Guide

Hướng dẫn sử dụng API gửi tin nhắn mới với hỗ trợ reply, modify, và attachments.

## Tổng quan

API mới hỗ trợ:
- ✅ Gửi tin nhắn text đơn giản (legacy)
- ✅ Reply tin nhắn (Facebook Messenger-style)
- ✅ Modify tin nhắn với cascade delete
- ✅ Gửi tin nhắn với attachments (file, image)
- ✅ Mixed requests (modify + new content)
- ✅ Modification details trong response

## Cách sử dụng

### 1. Sử dụng useChatStream (Unified API)

```typescript
import { useChatStream } from '@/shared/hooks/common/useChatStream';
import { MessageRequest } from '@/shared/types/chat-streaming.types';

const chatStream = useChatStream({
  getAuthToken: () => localStorage.getItem('authToken') || ''
});

// Gửi tin nhắn text đơn giản (legacy)
await chatStream.sendMessage('Hello!');

// Gửi tin nhắn với MessageRequest
const messageRequest: MessageRequest = {
  contentBlocks: [{ type: 'text', content: 'Hello with new API!' }],
  threadId: 'thread-123'
};
await chatStream.sendMessage(messageRequest);
```

### 2. Sử dụng useMessageSender (Helper Hook)

```typescript
import { useMessageSender } from '@/shared/hooks/common/useMessageSender';

const chatStream = useChatStream({ /* config */ });
const messageSender = useMessageSender(chatStream);

// Gửi text đơn giản
await messageSender.sendText('Hello!');

// Reply tin nhắn
await messageSender.replyTo('Thanks!', 'msg-456');

// Modify tin nhắn
await messageSender.modifyMessage('Corrected message', 'msg-789');

// Gửi với attachments
await messageSender.sendWithAttachments('Here are files:', [
  { type: 'file', id: 'file-123' },
  { type: 'image', id: 'img-456' }
]);
```

### 3. Sử dụng Utility Functions

```typescript
import {
  createTextMessage,
  createReplyMessage,
  createModifyMessage,
  createMessageWithAttachments
} from '@/shared/utils/message-request.utils';

// Tạo tin nhắn text
const textMsg = createTextMessage('Hello!', 'thread-123');

// Tạo reply message
const replyMsg = createReplyMessage('Thanks!', 'thread-123', 'msg-456');

// Tạo modify message
const modifyMsg = createModifyMessage('Corrected', 'thread-123', 'msg-789');

// Tạo message với attachments
const attachMsg = createMessageWithAttachments('Files:', 'thread-123', [
  { type: 'file', id: 'file-123' }
]);
```

### 4. Sử dụng Direct API

```typescript
import { AIAgentsApi } from '@/shared/api/ai-agents.api';

// Helper methods
await AIAgentsApi.sendTextMessage('Hello!', 'thread-123');
await AIAgentsApi.replyToMessage('Thanks!', 'thread-123', 'msg-456');
await AIAgentsApi.modifyMessage('Corrected', 'thread-123', 'msg-789');
await AIAgentsApi.sendMessageWithAttachments('Files:', 'thread-123', [
  { type: 'file', id: 'file-123' }
]);
```

## Các tính năng chính

### Reply Messages

```typescript
const replyRequest: MessageRequest = {
  contentBlocks: [{ type: 'text', content: 'Thanks for the info!' }],
  threadId: 'thread-123',
  replyToMessageId: 'msg-456' // Facebook Messenger-style flat reply
};

const response = await chatStream.sendMessage(replyRequest);
```

### Modify Messages

```typescript
const modifyRequest: MessageRequest = {
  contentBlocks: [{ type: 'text', content: 'Corrected message' }],
  threadId: 'thread-123',
  messageId: 'msg-789' // Message to modify
};

const response = await chatStream.sendMessage(modifyRequest);

// Response sẽ có modificationDetails
console.log(response.modificationDetails?.deletedMessagesCount);
```

### Attachments

```typescript
const attachmentRequest: MessageRequest = {
  contentBlocks: [
    { type: 'text', content: 'Here are the files:' },
    { type: 'file', fileId: 'file-123' },
    { type: 'image', imageId: 'img-456' }
  ],
  threadId: 'thread-123',
  attachmentContext: [
    { type: 'file', fileId: 'file-123' },
    { type: 'image', imageId: 'img-456' }
  ]
};
```

## Response Types

### SendMessageResponse (Legacy)

```typescript
interface SendMessageResponse {
  runId: string;
  agentId: string;
  agentName: string;
  status: string;
  createdAt: number;
  message: string;
}
```

### MessageResponse (New)

```typescript
interface MessageResponse {
  messageId: string;
  runId: string;
  agentName: string;
  status: string;
  createdAt: number;
  modificationDetails?: {
    modifiedMessageId: string;
    deletedMessageIds: string[];
    deletedMessagesCount: number;
    operationType: 'modify_text' | 'modify_content' | 'modify_attachments';
  };
}
```

## Validation

```typescript
import { validateMessageRequest } from '@/shared/utils/message-request.utils';

const validation = validateMessageRequest(messageRequest);
if (!validation.isValid) {
  console.error('Validation errors:', validation.errors);
}
```

## Constants

```typescript
import {
  MESSAGE_CONTENT_TYPES,
  MESSAGE_VALIDATION_RULES,
  MESSAGE_ERROR_MESSAGES
} from '@/shared/constants/message.constants';

// Sử dụng constants
const maxLength = MESSAGE_VALIDATION_RULES.MAX_TEXT_LENGTH;
const errorMsg = MESSAGE_ERROR_MESSAGES.TEXT_TOO_LONG;
```

## Best Practices

1. **Sử dụng useMessageSender** cho các use cases thông thường
2. **Validate request** trước khi gửi
3. **Handle errors** từ API response
4. **Check modificationDetails** khi modify messages
5. **Sử dụng constants** thay vì hardcode values
6. **Type safety** với TypeScript interfaces

## Migration từ API cũ

```typescript
// Cũ
await chatStream.sendMessage('Hello!');

// Mới (vẫn hoạt động)
await chatStream.sendMessage('Hello!');

// Hoặc sử dụng MessageRequest
await chatStream.sendMessage({
  contentBlocks: [{ type: 'text', content: 'Hello!' }],
  threadId: 'thread-123'
});
```

API mới hoàn toàn backward compatible với API cũ!
