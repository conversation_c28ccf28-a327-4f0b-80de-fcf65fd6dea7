# Chat Message API Guide

Hướng dẫn sử dụng API gửi tin nhắn chuẩn duy nhất với hỗ trợ đầy đủ tính năng.

## Tổng quan

API chuẩn duy nhất `ChatMessageRequest` hỗ trợ:
- ✅ Gửi tin nhắn text đơn giản
- ✅ Reply tin nhắn (Facebook Messenger-style flat reply)
- ✅ Modify tin nhắn với cascade delete
- ✅ Gửi tin nhắn với attachments (file, image)
- ✅ Tool call decisions (approve/reject/always)
- ✅ Mixed requests (modify + new content)
- ✅ Modification details trong response

## Cách sử dụng

### 1. Sử dụng useChatStream (Unified API)

```typescript
import { useChatStream } from '@/shared/hooks/common/useChatStream';
import { ChatMessageRequest } from '@/shared/types/chat-streaming.types';

const chatStream = useChatStream({
  getAuthToken: () => localStorage.getItem('authToken') || ''
});

// Gửi tin nhắn text đơn giản (legacy - vẫn hoạt động)
await chatStream.sendMessage('Hello!');

// Gửi tin nhắn với ChatMessageRequest
const messageRequest: ChatMessageRequest = {
  contentBlocks: [{ type: 'text', content: 'Hello with new API!' }],
  threadId: 'thread-123'
};
await chatStream.sendMessage(messageRequest);
```

### 2. Sử dụng useMessageSender (Helper Hook)

```typescript
import { useMessageSender } from '@/shared/hooks/common/useMessageSender';

const chatStream = useChatStream({ /* config */ });
const messageSender = useMessageSender(chatStream);

// Gửi text đơn giản
await messageSender.sendText('Hello!');

// Reply tin nhắn
await messageSender.replyTo('Thanks!', 'msg-456');

// Modify tin nhắn
await messageSender.modifyMessage('Corrected message', 'msg-789');

// Gửi với attachments
await messageSender.sendWithAttachments('Here are files:', [
  { type: 'file', id: 'file-123' },
  { type: 'image', id: 'img-456' }
]);

// Approve tool call
await messageSender.approveToolCall('yes');
await messageSender.approveToolCall('always'); // Auto-approve future calls
```

### 3. Sử dụng Utility Functions

```typescript
import {
  createTextMessage,
  createReplyMessage,
  createModifyMessage,
  createMessageWithAttachments
} from '@/shared/utils/message-request.utils';

// Tạo tin nhắn text
const textMsg = createTextMessage('Hello!', 'thread-123');

// Tạo reply message
const replyMsg = createReplyMessage('Thanks!', 'thread-123', 'msg-456');

// Tạo modify message
const modifyMsg = createModifyMessage('Corrected', 'thread-123', 'msg-789');

// Tạo message với attachments
const attachMsg = createMessageWithAttachments('Files:', 'thread-123', [
  { type: 'file', id: 'file-123' }
]);
```

### 4. Sử dụng Direct API

```typescript
import { AIAgentsApi } from '@/shared/api/ai-agents.api';

// Helper methods
await AIAgentsApi.sendTextMessage('Hello!', 'thread-123');
await AIAgentsApi.replyToMessage('Thanks!', 'thread-123', 'msg-456');
await AIAgentsApi.modifyMessage('Corrected', 'thread-123', 'msg-789');
await AIAgentsApi.sendMessageWithAttachments('Files:', 'thread-123', [
  { type: 'file', id: 'file-123' }
]);
await AIAgentsApi.approveToolCall('thread-123', 'yes');
```

## Các tính năng chính

### Reply Messages

```typescript
const replyRequest: ChatMessageRequest = {
  contentBlocks: [{ type: 'text', content: 'Thanks for the info!' }],
  threadId: 'thread-123',
  replyToMessageId: 'msg-456' // Facebook Messenger-style flat reply
};

const response = await chatStream.sendMessage(replyRequest);
```

### Modify Messages

```typescript
const modifyRequest: ChatMessageRequest = {
  contentBlocks: [{ type: 'text', content: 'Corrected message' }],
  threadId: 'thread-123',
  messageId: 'msg-789' // Message to modify
};

const response = await chatStream.sendMessage(modifyRequest);

// Response sẽ có modificationDetails
console.log(response.modificationDetails?.deletedMessagesCount);
```

### Attachments

```typescript
const attachmentRequest: ChatMessageRequest = {
  contentBlocks: [
    { type: 'text', content: 'Here are the files:' },
    { type: 'file', fileId: 'file-123' },
    { type: 'image', imageId: 'img-456' }
  ],
  threadId: 'thread-123',
  attachmentContext: [
    { type: 'file', fileId: 'file-123' },
    { type: 'image', imageId: 'img-456' }
  ]
};
```

### Tool Call Decisions

```typescript
// Approve tool call
const approveRequest: ChatMessageRequest = {
  contentBlocks: [{ type: 'tool_call_decision', decision: 'yes' }],
  threadId: 'thread-123'
};

// Always approve (auto-approve future calls)
const alwaysApproveRequest: ChatMessageRequest = {
  contentBlocks: [{ type: 'tool_call_decision', decision: 'always' }],
  threadId: 'thread-123',
  alwaysApproveToolCall: true // Required when decision = 'always'
};

// Reject tool call
const rejectRequest: ChatMessageRequest = {
  contentBlocks: [{ type: 'tool_call_decision', decision: 'no' }],
  threadId: 'thread-123'
};
```

## Response Types

### ChatMessageResponse (Chuẩn duy nhất)

```typescript
interface ChatMessageResponse {
  messageId: string;
  runId: string;
  agentName: string;
  status: string;
  createdAt: number;
  modificationDetails?: {
    modifiedMessageId: string;
    deletedMessageIds: string[];
    deletedMessagesCount: number;
    operationType: 'modify_text' | 'modify_content' | 'modify_attachments';
  };
}
```

### Response Examples

```typescript
// Regular message response
{
  messageId: '0280c24b-c849-492d-b5fd-e1c927186272',
  runId: 'run_123456-789-abc',
  agentName: 'Customer Support Agent',
  status: 'created',
  createdAt: 1672531200000,
  modificationDetails: {
    modifiedMessageId: '',
    deletedMessageIds: [],
    deletedMessagesCount: 0
  }
}

// Modify-only response
{
  messageId: 'msg_123_modified',
  runId: 'run_456',
  agentName: 'Assistant',
  status: 'created',
  createdAt: 1749708424552,
  modificationDetails: {
    modifiedMessageId: 'msg_123',
    deletedMessageIds: ['msg_124', 'msg_125'],
    deletedMessagesCount: 2
  }
}
```

## Validation

```typescript
import { validateMessageRequest } from '@/shared/utils/message-request.utils';

const validation = validateMessageRequest(messageRequest);
if (!validation.isValid) {
  console.error('Validation errors:', validation.errors);
}
```

## Constants

```typescript
import {
  MESSAGE_CONTENT_TYPES,
  MESSAGE_VALIDATION_RULES,
  MESSAGE_ERROR_MESSAGES
} from '@/shared/constants/message.constants';

// Sử dụng constants
const maxLength = MESSAGE_VALIDATION_RULES.MAX_TEXT_LENGTH;
const errorMsg = MESSAGE_ERROR_MESSAGES.TEXT_TOO_LONG;
```

## Best Practices

1. **Sử dụng useMessageSender** cho các use cases thông thường
2. **Validate request** trước khi gửi
3. **Handle errors** từ API response
4. **Check modificationDetails** khi modify messages
5. **Sử dụng constants** thay vì hardcode values
6. **Type safety** với TypeScript interfaces

## Migration từ API cũ

```typescript
// Cũ
await chatStream.sendMessage('Hello!');

// Mới (vẫn hoạt động)
await chatStream.sendMessage('Hello!');

// Hoặc sử dụng MessageRequest
await chatStream.sendMessage({
  contentBlocks: [{ type: 'text', content: 'Hello!' }],
  threadId: 'thread-123'
});
```

API mới hoàn toàn backward compatible với API cũ!
