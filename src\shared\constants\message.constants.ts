/**
 * Constants cho message system
 */

/**
 * Các loại content block được hỗ trợ
 */
export const MESSAGE_CONTENT_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  FILE: 'file',
  TOOL_CALL_DECISION: 'tool_call_decision'
} as const;

/**
 * Các loại attachment được hỗ trợ
 */
export const ATTACHMENT_TYPES = {
  FILE: 'file',
  IMAGE: 'image'
} as const;

/**
 * Các loại operation modification
 */
export const MODIFICATION_OPERATION_TYPES = {
  MODIFY_TEXT: 'modify_text',
  MODIFY_CONTENT: 'modify_content',
  MODIFY_ATTACHMENTS: 'modify_attachments'
} as const;

/**
 * Validation rules cho message
 */
export const MESSAGE_VALIDATION_RULES = {
  /**
   * Độ dài tối đa của text content
   */
  MAX_TEXT_LENGTH: 10000,

  /**
   * <PERSON><PERSON> lượng tối đa content blocks
   */
  MAX_CONTENT_BLOCKS: 20,

  /**
   * <PERSON><PERSON> lượng tối đa attachments
   */
  MAX_ATTACHMENTS: 10,

  /**
   * Các file extensions được phép cho file attachments
   */
  ALLOWED_FILE_EXTENSIONS: [
    '.pdf', '.doc', '.docx', '.txt', '.md',
    '.xls', '.xlsx', '.csv',
    '.ppt', '.pptx',
    '.zip', '.rar', '.7z'
  ],

  /**
   * Các image extensions được phép
   */
  ALLOWED_IMAGE_EXTENSIONS: [
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'
  ],

  /**
   * Kích thước file tối đa (bytes)
   */
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB

  /**
   * Kích thước image tối đa (bytes)
   */
  MAX_IMAGE_SIZE: 10 * 1024 * 1024 // 10MB
} as const;

/**
 * Error messages cho validation
 */
export const MESSAGE_ERROR_MESSAGES = {
  THREAD_ID_REQUIRED: 'Thread ID is required',
  CONTENT_BLOCKS_EMPTY: 'Content blocks cannot be empty',
  TEXT_CONTENT_REQUIRED: 'Text content is required for text blocks',
  FILE_ID_REQUIRED: 'File ID is required for file blocks',
  IMAGE_ID_REQUIRED: 'Image ID is required for image blocks',
  TEXT_TOO_LONG: `Text content exceeds maximum length of ${MESSAGE_VALIDATION_RULES.MAX_TEXT_LENGTH} characters`,
  TOO_MANY_CONTENT_BLOCKS: `Too many content blocks. Maximum allowed: ${MESSAGE_VALIDATION_RULES.MAX_CONTENT_BLOCKS}`,
  TOO_MANY_ATTACHMENTS: `Too many attachments. Maximum allowed: ${MESSAGE_VALIDATION_RULES.MAX_ATTACHMENTS}`,
  INVALID_CONTENT_TYPE: 'Invalid content block type',
  INVALID_ATTACHMENT_TYPE: 'Invalid attachment type',
  BOTH_REPLY_AND_MODIFY: 'Cannot have both replyToMessageId and messageId',
  INVALID_FILE_EXTENSION: 'Invalid file extension',
  INVALID_IMAGE_EXTENSION: 'Invalid image extension',
  FILE_TOO_LARGE: `File size exceeds maximum of ${MESSAGE_VALIDATION_RULES.MAX_FILE_SIZE / (1024 * 1024)}MB`,
  IMAGE_TOO_LARGE: `Image size exceeds maximum of ${MESSAGE_VALIDATION_RULES.MAX_IMAGE_SIZE / (1024 * 1024)}MB`
} as const;

/**
 * Default values cho message request
 */
export const MESSAGE_DEFAULTS = {
  ALWAYS_APPROVE_TOOL_CALL: false,
  THREAD_NAME: 'Cuộc trò chuyện mới'
} as const;

/**
 * Message status constants
 */
export const MESSAGE_STATUS = {
  PENDING: 'pending',
  SENT: 'sent',
  DELIVERED: 'delivered',
  READ: 'read',
  FAILED: 'failed',
  STREAMING: 'streaming',
  COMPLETED: 'completed'
} as const;

/**
 * Message sender types
 */
export const MESSAGE_SENDER = {
  USER: 'user',
  AI: 'ai',
  SYSTEM: 'system',
  WORKER: 'worker'
} as const;

/**
 * SSE Event types liên quan đến messages
 */
export const MESSAGE_SSE_EVENTS = {
  TOKEN: 'token',
  MESSAGE: 'message',
  ERROR: 'error',
  COMPLETE: 'complete',
  THINKING: 'thinking',
  TOOL_CALL: 'tool_call',
  STREAM_START: 'stream_start',
  STREAM_END: 'stream_end',
  STREAM_ERROR: 'stream_error'
} as const;

/**
 * API endpoints cho messages
 */
export const MESSAGE_API_ENDPOINTS = {
  SEND_MESSAGE: '/chat/message',
  STOP_RUN: '/chat/runs/{runId}/stop',
  GET_MESSAGES: '/chat/threads/{threadId}/messages',
  GET_MESSAGE: '/chat/messages/{messageId}',
  DELETE_MESSAGE: '/chat/messages/{messageId}'
} as const;

/**
 * Timeout values (milliseconds)
 */
export const MESSAGE_TIMEOUTS = {
  API_REQUEST: 30000, // 30 seconds
  SSE_CONNECTION: 60000, // 1 minute
  RETRY_DELAY: 1000, // 1 second
  MAX_RETRY_ATTEMPTS: 3
} as const;

/**
 * Helper function để check content type
 */
export function isValidContentType(type: string): boolean {
  return Object.values(MESSAGE_CONTENT_TYPES).includes(type as any);
}

/**
 * Helper function để check attachment type
 */
export function isValidAttachmentType(type: string): boolean {
  return Object.values(ATTACHMENT_TYPES).includes(type as any);
}

/**
 * Helper function để check file extension
 */
export function isValidFileExtension(filename: string): boolean {
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
  return MESSAGE_VALIDATION_RULES.ALLOWED_FILE_EXTENSIONS.includes(extension);
}

/**
 * Helper function để check image extension
 */
export function isValidImageExtension(filename: string): boolean {
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
  return MESSAGE_VALIDATION_RULES.ALLOWED_IMAGE_EXTENSIONS.includes(extension);
}

/**
 * Helper function để format file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Helper function để validate file size
 */
export function isValidFileSize(size: number, isImage: boolean = false): boolean {
  const maxSize = isImage ? MESSAGE_VALIDATION_RULES.MAX_IMAGE_SIZE : MESSAGE_VALIDATION_RULES.MAX_FILE_SIZE;
  return size <= maxSize;
}
