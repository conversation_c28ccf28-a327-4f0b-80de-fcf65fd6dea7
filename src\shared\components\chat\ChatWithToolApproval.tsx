/**
 * ChatWithToolApproval Component
 * Example component showing how to integrate tool call approval with chat
 */

import React, { useEffect } from 'react';
import { useChatStream } from '@/shared/hooks/common/useChatStream';
import { useToolCallApproval } from '@/shared/hooks/chat/useToolCallApproval';
import ToolCallApprovalForm from './ToolCallApprovalForm';

export interface ChatWithToolApprovalProps {
  /**
   * Agent ID để chat
   */
  agentId?: string;

  /**
   * Thread ID (optional)
   */
  threadId?: string;

  /**
   * Auth token getter
   */
  getAuthToken?: () => string | Promise<string>;
}

/**
 * ChatWithToolApproval Component
 */
const ChatWithToolApproval: React.FC<ChatWithToolApprovalProps> = ({
  agentId,
  threadId,
  getAuthToken
}) => {
  // Chat stream hook
  const chatStream = useChatStream({
    agentId,
    getAuthToken,
    debug: true
  });

  // Tool call approval hook
  const toolApproval = useToolCallApproval({
    onApprove: async (decision, toolCallData) => {
      // Gọi approveToolCall từ chat stream
      await chatStream.approveToolCall(decision);
    },
    onDismiss: (toolCallData) => {
      console.log('Tool call approval dismissed:', toolCallData.toolName);
      // Có thể gọi dismiss function từ chat stream nếu cần
      chatStream.dismissToolCallInterrupt();
    },
    autoDismissOnSuccess: true
  });

  // Listen for tool call interrupts từ chat stream
  useEffect(() => {
    if (chatStream.toolCallInterrupt) {
      console.log('Tool call interrupt detected:', chatStream.toolCallInterrupt);
      toolApproval.showApprovalForm(chatStream.toolCallInterrupt);
    }
  }, [chatStream.toolCallInterrupt, toolApproval]);

  // Load thread nếu có threadId
  useEffect(() => {
    if (threadId) {
      chatStream.loadSpecificThread(threadId);
    } else {
      chatStream.loadLatestThread();
    }
  }, [threadId]);

  const handleSendMessage = async (message: string) => {
    try {
      await chatStream.sendMessage(message);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Chat with Tool Approval
          </h2>
          <div className="flex items-center space-x-2">
            {chatStream.isConnected && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                Connected
              </span>
            )}
            {chatStream.isThinking && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-1 animate-pulse"></div>
                Thinking...
              </span>
            )}
          </div>
        </div>
        
        {chatStream.threadName && (
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Thread: {chatStream.threadName}
          </p>
        )}
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {chatStream.messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.sender === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white'
              }`}
            >
              <p className="text-sm">{message.content}</p>
              {message.status === 'streaming' && (
                <div className="mt-1">
                  <div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Current streaming text */}
        {chatStream.currentStreamingText && (
          <div className="flex justify-start">
            <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white">
              <p className="text-sm">{chatStream.currentStreamingText}</p>
              <div className="mt-1">
                <div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        )}

        {/* Worker thinking */}
        {chatStream.workerThinking.isVisible && (
          <div className="flex justify-center">
            <div className="px-4 py-2 rounded-lg bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
              <p className="text-sm">🤔 {chatStream.workerThinking.content || 'Thinking...'}</p>
            </div>
          </div>
        )}
      </div>

      {/* Input Area */}
      <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex space-x-2">
          <input
            type="text"
            placeholder="Type your message..."
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            onKeyPress={(e) => {
              if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                handleSendMessage(e.currentTarget.value);
                e.currentTarget.value = '';
              }
            }}
            disabled={chatStream.isLoading || chatStream.isStreaming}
          />
          <button
            onClick={() => {
              const input = document.querySelector('input[type="text"]') as HTMLInputElement;
              if (input?.value.trim()) {
                handleSendMessage(input.value);
                input.value = '';
              }
            }}
            disabled={chatStream.isLoading || chatStream.isStreaming}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors"
          >
            {chatStream.isLoading || chatStream.isStreaming ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              'Send'
            )}
          </button>
        </div>

        {/* Error Display */}
        {chatStream.error && (
          <div className="mt-2 p-2 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded text-sm">
            {chatStream.error}
          </div>
        )}

        {/* Tool Approval Error */}
        {toolApproval.approvalError && (
          <div className="mt-2 p-2 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded text-sm">
            Tool Approval Error: {toolApproval.approvalError}
            <button
              onClick={toolApproval.clearApprovalError}
              className="ml-2 text-red-500 hover:text-red-700"
            >
              ✕
            </button>
          </div>
        )}
      </div>

      {/* Tool Call Approval Form */}
      {toolApproval.toolCallData && (
        <ToolCallApprovalForm
          toolCallData={toolApproval.toolCallData}
          onApprove={toolApproval.handleApproval}
          onDismiss={toolApproval.hideApprovalForm}
          isLoading={toolApproval.isApproving}
        />
      )}
    </div>
  );
};

export default ChatWithToolApproval;
