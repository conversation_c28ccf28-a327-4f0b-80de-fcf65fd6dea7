/**
 * Hooks for audience API using TanStack Query
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AudienceService } from '../services/audience.service';
import {
  AudienceQueryParams,
  CreateAudienceRequest,
  UpdateAudienceRequest,
  ContactData,
  BulkUpdateCustomFieldsDto,
} from '../types/audience.types';

/**
 * Query keys for audience API
 */
export const AUDIENCE_QUERY_KEYS = {
  all: ['marketing', 'audiences'] as const,
  list: (params: AudienceQueryParams) => [...AUDIENCE_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...AUDIENCE_QUERY_KEYS.all, 'detail', id] as const,
};

/**
 * Hook to get audiences with pagination and filtering
 */
export const useAudiences = (params: AudienceQueryParams = {}) => {
  console.log('useAudiences hook called with params:', params); // Debug log
  return useQuery({
    queryKey: AUDIENCE_QUERY_KEYS.list(params),
    queryFn: () => {
      console.log('useAudiences queryFn called with params:', params); // Debug log
      return AudienceService.getAudiences(params);
    },
    select: data => {
      console.log('Raw API Response:', data); // Debug log

      // Chuyển đổi cấu trúc dữ liệu từ API để phù hợp với frontend
      // API trả về result.data với ContactData[] thay vì result.items với Audience[]
      if (data.result && data.result.data) {
        const contactData = data.result.data as ContactData[];
        return {
          items: contactData,
          meta: {
            totalItems: data.result.meta?.totalItems || contactData.length,
            itemCount: contactData.length,
            itemsPerPage: data.result.meta?.itemsPerPage || 10,
            totalPages: data.result.meta?.totalPages || Math.ceil(contactData.length / 10),
            currentPage: data.result.meta?.currentPage || 1,
          },
        };
      }

      // Fallback cho cấu trúc cũ nếu API trả về result.items
      if (data.result && 'items' in data.result) {
        const fallbackResult = data.result as {
          items: ContactData[];
          meta?: {
            totalItems?: number;
            itemsPerPage?: number;
            totalPages?: number;
            currentPage?: number;
          };
        };
        const items = fallbackResult.items;
        return {
          items: items,
          meta: {
            totalItems: fallbackResult.meta?.totalItems || items.length,
            itemCount: items.length,
            itemsPerPage: fallbackResult.meta?.itemsPerPage || 10,
            totalPages: fallbackResult.meta?.totalPages || Math.ceil(items.length / 10),
            currentPage: fallbackResult.meta?.currentPage || 1,
          },
        };
      }

      return {
        items: [],
        meta: { totalItems: 0, itemCount: 0, itemsPerPage: 10, totalPages: 0, currentPage: 1 },
      };
    },
  });
};

/**
 * Hook to get audience by ID
 */
export const useAudience = (id: number) => {
  return useQuery({
    queryKey: AUDIENCE_QUERY_KEYS.detail(id),
    queryFn: () => AudienceService.getAudienceById(id),
    select: data => {
      // Chuyển đổi cấu trúc dữ liệu từ API để phù hợp với frontend
      if (data.result) {
        return data.result;
      }
      return null;
    },
    enabled: !!id,
  });
};

/**
 * Hook to create audience
 */
export const useCreateAudience = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAudienceRequest) => {
      console.log('useCreateAudience mutationFn called with:', data); // Debug log
      return AudienceService.createAudience(data);
    },
    onSuccess: (data) => {
      console.log('useCreateAudience onSuccess called with:', data); // Debug log
      queryClient.invalidateQueries({ queryKey: AUDIENCE_QUERY_KEYS.all });
    },
    onError: (error) => {
      console.error('useCreateAudience onError called with:', error); // Debug log
    },
  });
};

/**
 * Hook to update audience
 */
export const useUpdateAudience = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateAudienceRequest) => AudienceService.updateAudience(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: AUDIENCE_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: AUDIENCE_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to delete audience
 */
export const useDeleteAudience = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => AudienceService.deleteAudience(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: AUDIENCE_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to delete multiple audiences
 */
export const useDeleteMultipleAudiences = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: number[]) => AudienceService.deleteMultipleAudiences(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: AUDIENCE_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to bulk update custom fields for audience
 */
export const useBulkUpdateAudienceCustomFields = (audienceId: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkUpdateCustomFieldsDto) =>
      AudienceService.bulkUpdateCustomFields(audienceId, data),
    onSuccess: () => {
      // Invalidate audience detail query to refetch updated data
      queryClient.invalidateQueries({ queryKey: AUDIENCE_QUERY_KEYS.detail(audienceId) });
      queryClient.invalidateQueries({ queryKey: AUDIENCE_QUERY_KEYS.all });
    },
    onError: (error) => {
      console.error('Error bulk updating audience custom fields:', error);
    },
  });
};
