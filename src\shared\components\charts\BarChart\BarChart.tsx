import React, { useMemo } from 'react';
import {
  Bar<PERSON>hart as RechartsBar<PERSON>hart,
  Bar,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer,
  Label,
  LabelList,
} from 'recharts';
import { BarChartProps } from './BarChart.types';
import { useTheme } from '@/shared/contexts/theme';
import { useTranslation } from 'react-i18next';

/**
 * BarChart component hiển thị dữ liệu dạng cột
 *
 * @example
 * ```tsx
 * <BarChart
 *   data={[
 *     { month: 'Jan', sales: 100, profit: 50 },
 *     { month: 'Feb', sales: 200, profit: 100 },
 *     { month: 'Mar', sales: 150, profit: 75 },
 *   ]}
 *   xAxisKey="month"
 *   bars={[
 *     { dataKey: 'sales', name: 'Sales', color: '#FF3333' },
 *     { dataKey: 'profit', name: 'Profit', color: '#FFCC99' },
 *   ]}
 *   height={300}
 *   showGrid
 *   showTooltip
 *   showLegend
 * />
 * ```
 */
const BarChart: React.FC<BarChartProps> = ({
  data,
  xAxisKey,
  bars,
  height = 300,
  width = '100%',
  showGrid = true,
  showTooltip = true,
  showLegend = true,
  legendPosition = 'bottom',
  margin = { top: 20, right: 30, left: 20, bottom: 20 },
  xAxisFormatter,
  yAxisFormatter,
  xAxisLabel,
  yAxisLabel,
  stacked = false,
  animated = true,
  className = '',
  layout = 'horizontal',
}) => {
  const { currentTheme } = useTheme();
  const { t } = useTranslation();

  // Lấy màu từ theme
  const gridColor = useMemo(
    () => currentTheme?.semanticColors?.border || '#E5E7EB',
    [currentTheme]
  );
  const textColor = useMemo(
    () => currentTheme?.semanticColors?.foreground || '#111827',
    [currentTheme]
  );

  // Tạo các cột
  const renderBars = useMemo(() => {
    return bars.map((bar, index) => (
      <Bar
        key={`bar-${index}`}
        dataKey={bar.dataKey}
        name={bar.name || t(`chart.${bar.dataKey}`, bar.dataKey)}
        fill={bar.color || currentTheme?.semanticColors?.primary || '#3B82F6'}
        barSize={bar.barSize ? bar.barSize * 100 : 0}
        radius={bar.radius || 0}
        isAnimationActive={bar.animated !== false && animated}
        stackId={stacked ? 'stack' : ''}
      >
        {bar.showLabel && (
          <LabelList
            dataKey={bar.dataKey}
            position={bar.labelPosition || 'top'}
            formatter={bar.labelFormatter || (() => '')}
            fill={textColor}
          />
        )}
      </Bar>
    ));
  }, [bars, stacked, animated, currentTheme, t, textColor]);

  // Tạo component chính
  const chart = (
    <RechartsBarChart data={data} margin={margin} className={className} layout={layout}>
      {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />}

      <XAxis
        dataKey={layout === 'vertical' ? '' : xAxisKey}
        type={layout === 'vertical' ? 'number' : 'category'}
        tick={{ fill: textColor }}
        tickFormatter={xAxisFormatter || (() => '')}
      >
        {xAxisLabel && <Label value={xAxisLabel} position="bottom" fill={textColor} />}
      </XAxis>

      <YAxis
          dataKey={layout === 'vertical' ? xAxisKey : ''}
        type={layout === 'vertical' ? 'category' : 'number'}
        tick={{ fill: textColor }}
        tickFormatter={yAxisFormatter || (() => '')}
      >
        {yAxisLabel && <Label value={yAxisLabel} angle={-90} position="left" fill={textColor} />}
      </YAxis>

      {showTooltip && <Tooltip wrapperStyle={{ zIndex: 9999 }} />}

      {showLegend && (
        <Legend
          verticalAlign={
            legendPosition === 'top' || legendPosition === 'bottom' ? legendPosition : 'middle'
          }
          align={
            legendPosition === 'left' || legendPosition === 'right' ? legendPosition : 'center'
          }
          wrapperStyle={{ color: textColor }}
        />
      )}

      {renderBars}
    </RechartsBarChart>
  );

  // Sử dụng div với kích thước cố định và ResponsiveContainer
  return (
    <div style={{ width, height, position: 'relative', minHeight: '300px' }}>
      {/* Sử dụng aspect ratio để đảm bảo biểu đồ luôn hiển thị */}
      <ResponsiveContainer width="100%" height="100%" aspect={16 / 9}>
        {chart}
      </ResponsiveContainer>
    </div>
  );
};

export default BarChart;
