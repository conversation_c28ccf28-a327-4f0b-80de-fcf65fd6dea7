{"marketing": {"title": "Marketing", "description": "Manage marketing campaigns and target audiences", "menu": {"audience": "Audience", "segment": "Segment", "campaign": "Campaign", "tags": "Tags", "customFields": "Custom Fields", "reports": "Reports", "templateEmail": "Email Templates"}, "common": {"all": "All", "active": "Active", "inactive": "Inactive", "draft": "Draft", "add": "Add New", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "search": "Search", "filter": "Filter", "actions": "Actions", "status": "Status", "name": "Name", "description": "Description", "createdAt": "Created At", "updatedAt": "Updated At", "type": "Type", "id": "ID", "followers": "Followers", "manage": "Manage"}, "audience": {"title": "Audience Management", "description": "Manage leads and customers", "manage": "Manage Audience", "addNew": "Add New Audience", "edit": "Edit Audience", "detail": {"overview": "Overview", "totalContacts": "Total Contacts", "type": "Type", "status": "Status"}, "name": "Audience Name", "email": "Email", "phone": "Phone Number", "type": {"customer": "Customer", "lead": "Lead", "subscriber": "Subscriber", "custom": "Custom"}, "status": "Status", "tags": "Tags", "customFields": {"searchPlaceholder": "Enter keywords to search for custom fields...", "noFields": "No custom fields yet. Use the search box above to add fields."}, "generalInfo": "General Information", "detailForm": "Audience Details", "loadError": "Error loading audience information", "notFound": "Audience not found", "notFoundDescription": "This audience may have been deleted or does not exist", "createSuccess": "Audience created successfully", "createError": "Failed to create audience", "totalContacts": "Total Contacts", "attributes": "Attributes", "confirmDelete": "Are you sure you want to delete this audience?", "overview": {"basicInfo": "Basic Information", "contactInfo": "Contact Information", "tags": "Tags", "timeInfo": "Time Information", "customFields": "Custom Fields"}, "types": {"customer": "Customer", "lead": "Lead", "subscriber": "Subscriber", "custom": "Custom"}, "statuses": {"active": "Active", "inactive": "Inactive", "draft": "Draft"}, "form": {"nameLabel": "Audience Name", "namePlaceholder": "Enter audience name", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter audience description", "typeLabel": "Type", "statusLabel": "Status", "attributesLabel": "Attributes", "addAttribute": "Add Attribute", "attributeName": "Attribute Name", "attributeValue": "Value"}}, "segment": {"title": "Segmentation", "description": "Segment customers by different criteria", "manage": "Manage Segments", "addNew": "Add New Segment", "edit": "Edit Segment", "detail": "Segment Details", "name": "Segment Name", "audience": "Audience", "status": "Status", "totalContacts": "Total Contacts", "conditions": "Conditions", "confirmDelete": "Are you sure you want to delete this segment?", "totalSegments": "Total Segments", "types": {"demographic": "Demographic", "behavioral": "Behavioral", "geographic": "Geographic", "custom": "Custom"}, "statuses": {"active": "Active", "inactive": "Inactive", "draft": "Draft"}, "operators": {"equals": "Equals", "not_equals": "Not Equals", "contains": "Contains", "not_contains": "Not Contains", "greater_than": "Greater Than", "less_than": "Less Than", "between": "Between"}, "form": {"title": "Segment Information", "name": "Segment Name", "namePlaceholder": "Enter segment name...", "description": "Description", "descriptionPlaceholder": "Enter segment description...", "conditions": "Conditions", "conditionsDescription": "Set up conditions to segment customers", "addGroup": "Add Condition Group", "addCondition": "Add Condition", "removeGroup": "Remove Group", "removeCondition": "Remove Condition", "field": "Field", "operator": "Operator", "value": "Value", "valuePlaceholder": "Enter value...", "and": "AND", "or": "OR", "operators": {"equals": "Equals", "not_equals": "Not Equals", "contains": "Contains", "not_contains": "Does Not Contain", "starts_with": "Starts With", "ends_with": "Ends With", "greater_than": "Greater Than", "less_than": "Less Than", "greater_than_or_equal": "Greater Than or Equal", "less_than_or_equal": "Less Than or Equal", "is_empty": "Is Empty", "is_not_empty": "Is Not Empty"}, "validation": {"nameRequired": "Segment name is required", "nameMinLength": "Segment name must be at least 2 characters", "nameMaxLength": "Segment name cannot exceed 100 characters", "descriptionMaxLength": "Description cannot exceed 500 characters", "conditionsRequired": "At least one condition is required", "fieldRequired": "Field is required", "operatorRequired": "Operator is required", "valueRequired": "Value is required"}, "buttons": {"save": "Save", "cancel": "Close", "saveTooltip": "Save segment", "cancelTooltip": "Cancel and close form"}, "systemField": "System Field"}, "selectCustomField": "Select field..."}, "campaign": {"title": "Campaigns", "description": "Manage marketing campaigns", "manage": "Manage Campaigns", "addNew": "Add New Campaign", "edit": "Edit Campaign", "detail": "Campaign Details", "name": "Campaign Name", "type": "Type", "segment": "Segment", "audience": "Audience", "totalContacts": "Total Contacts", "startDate": "Start Date", "endDate": "End Date", "confirmDelete": "Are you sure you want to delete this campaign?", "activeCampaigns": "Active Campaigns", "types": {"email": "Email", "sms": "SMS", "push": "Push Notification", "social": "Social Media", "multi": "Multi-Channel"}, "status": {"active": "Active", "paused": "Paused", "completed": "Completed", "scheduled": "Scheduled", "draft": "Draft"}, "metrics": {"sent": "<PERSON><PERSON>", "delivered": "Delivered", "opened": "Opened", "clicked": "Clicked", "converted": "Converted", "bounced": "Bounced", "unsubscribed": "Unsubscribed"}, "form": {"nameLabel": "Campaign Name", "namePlaceholder": "Enter campaign name", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter campaign description", "typeLabel": "Type", "statusLabel": "Status", "segmentLabel": "Segment", "segmentPlaceholder": "Select segment", "audienceLabel": "Audience", "audiencePlaceholder": "Select audience", "startDateLabel": "Start Date", "endDateLabel": "End Date", "endDateOptional": "(Optional)"}}, "tags": {"title": "Tag Management", "description": "Create and manage tags for customers", "addNew": "Add New Tag", "edit": "Edit Tag", "name": "Tag Name", "status": "Status", "confirmDelete": "Are you sure you want to delete this tag?", "statuses": {"active": "Active", "inactive": "Inactive"}, "form": {"name": "Tag Name", "namePlaceholder": "Enter tag name", "color": "Color Code", "colorPlaceholder": "Choose color for tag", "randomColor": "Random Color"}, "validation": {"nameRequired": "Tag name is required", "colorInvalid": "Color code must be in HEX format (e.g., #FF0000)"}, "deleteSuccess": "Tag deleted successfully", "deleteError": "Failed to delete tag", "selectToDelete": "Please select tags to delete", "bulkDeleteSuccess": "Successfully deleted {{count}} tags", "bulkDeleteError": "Failed to delete multiple tags", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected tags?", "totalTags": "Total Tags", "manage": "Manage Tags"}, "tag": {"validation": {"nameRequired": "Tag name is required", "colorInvalid": "Color code must be in HEX format (e.g., #FF0000)"}}, "customField": {"configId": "Field Identifier Name", "title": "Custom Fields", "description": "Manage custom fields", "adminDescription": "Manage system custom fields", "add": "Add Custom Field", "edit": "Edit Custom Field", "addForm": "Add New Custom Field", "editForm": "Edit Custom Field", "component": "Component Type", "components": {"input": "Input Field", "textarea": "Text Area", "select": "Select List", "checkbox": "Checkbox", "radio": "Radio Button", "date": "Date", "number": "Number", "file": "File", "multiSelect": "Multi Select"}, "type": "Data Type", "type.string": "Text", "type.number": "Number", "type.boolean": "Yes/No", "type.date": "Date", "type.object": "Object", "type.array": "Array", "types": {"text": "Text", "number": "Number", "boolean": "Yes/No", "date": "Date", "select": "Select Box", "object": "Object", "array": "Array", "string": "Text"}, "name": "Field Name", "label": "Label", "placeholder": "Placeholder", "defaultValue": "Default Value", "options": "Options", "required": "Required", "validation": {"minLength": "Minimum Length", "maxLength": "Maximum Length", "pattern": "Pattern", "min": "Minimum Value", "max": "Maximum Value"}, "form": {"componentRequired": "Please select component type", "labelRequired": "Please enter label", "typeRequired": "Please select data type", "idRequired": "Please enter field identifier name", "labelPlaceholder": "Enter display label", "descriptionPlaceholder": "Enter description for this field", "placeholderPlaceholder": "Enter placeholder", "defaultValuePlaceholder": "Enter default value", "optionsPlaceholder": "Enter options, separated by commas or JSON format", "selectOptionsPlaceholder": "Enter values in format: Name|Value, Each pair on a new line. Example:\na|1\nb|2", "booleanDefaultPlaceholder": "Select default value", "dateDefaultPlaceholder": "Select default date", "description": "Description", "labelTagRequired": "Please add at least one label", "fieldIdLabel": "Field Identifier Name", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "Field Display Name", "displayNamePlaceholder": "Enter display name for this field", "displayNameRequired": "Please enter field display name", "labelInputPlaceholder": "Enter label and press Enter", "tagsCount": "labels added", "patternSuggestions": "Common pattern suggestions:", "defaultValue": "Default Value", "minLength": "Minimum Length", "maxLength": "Maximum Length", "pattern": "Pattern", "options": "Options", "min": "Minimum Value", "max": "Maximum Value", "placeholder": "Placeholder", "required": "Required", "labels": "Labels", "showAdvancedSettings": "Show Advanced Settings"}, "createSuccess": "Custom field created successfully", "createError": "Error creating custom field", "updateSuccess": "Custom field updated successfully", "updateError": "Error updating custom field", "deleteSuccess": "Custom field deleted successfully", "deleteError": "Error deleting custom field", "loadError": "Error loading custom field", "booleanValues": {"true": "Yes", "false": "No"}, "patterns": {"email": "Email", "phoneVN": "Vietnam Phone", "phoneIntl": "International Phone", "postalCodeVN": "Vietnam Postal Code", "lettersOnly": "Letters Only", "numbersOnly": "Numbers Only", "alphanumeric": "Letters and Numbers", "noSpecialChars": "No Special Characters", "url": "URL", "ipv4": "IPv4", "strongPassword": "Strong Password", "vietnameseName": "Vietnamese Name", "studentId": "Student ID", "nationalId": "National ID", "taxCode": "Tax Code", "dateFormat": "Date (dd/mm/yyyy)", "timeFormat": "Time (hh:mm)", "hexColor": "Hex Color", "base64": "Base64", "uuid": "UUID", "filename": "Filename", "urlSlug": "URL Slug", "variableName": "Variable Name", "creditCard": "Credit Card Number", "qrCode": "QR Code", "gpsCoordinate": "GPS Coordinate", "rgbColor": "RGB Color", "domain": "Domain", "decimal": "Decimal", "barcode": "Barcode"}, "confirmDeleteMessage": "Are you sure you want to delete this custom field?", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected custom fields?", "bulkDeleteSuccess": "Successfully deleted {{count}} custom fields", "bulkDeleteError": "Error occurred while deleting custom fields", "selectedItems": "Selected {{count}} items", "totalFields": "Total Custom Fields", "manage": "Manage Custom Fields", "noDescription": "No description"}, "reports": {"title": "Reports", "description": "View marketing activity reports", "overview": "Overview", "campaigns": "Campaigns", "audiences": "Audiences", "segments": "Segments", "performance": "Performance", "period": "Time Period", "exportData": "Export Data", "metrics": {"totalCampaigns": "Total Campaigns", "activeCampaigns": "Active Campaigns", "totalAudiences": "Total Audiences", "totalSegments": "Total Segments", "conversionRate": "Conversion Rate", "engagementRate": "Engagement Rate"}, "periods": {"today": "Today", "yesterday": "Yesterday", "thisWeek": "This Week", "lastWeek": "Last Week", "thisMonth": "This Month", "lastMonth": "Last Month", "custom": "Custom"}, "totalReports": "Total Reports", "view": "View Reports"}, "templateEmail": {"title": "Email Templates", "description": "Create and manage email templates", "manage": "Manage Email Templates", "addNew": "Add New Email Template", "edit": "Edit <PERSON><PERSON>late", "detail": "Email Template Details", "name": "Template Name", "subject": "Email Subject", "content": "Email Content", "type": "Template Type", "status": "Status", "confirmDelete": "Are you sure you want to delete this email template?", "types": {"welcome": "Welcome", "newsletter": "Newsletter", "promotion": "Promotion", "notification": "Notification", "transactional": "Transactional", "custom": "Custom"}, "statuses": {"active": "Active", "inactive": "Inactive", "draft": "Draft"}, "form": {"nameLabel": "Template Name", "namePlaceholder": "Enter template name", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter template description", "subjectLabel": "Email Subject", "subjectPlaceholder": "Enter email subject", "contentLabel": "Email Content", "contentPlaceholder": "Enter email content", "typeLabel": "Template Type", "statusLabel": "Status"}}, "emailMarketing": {"title": "Email Marketing", "description": "Manage email campaigns and templates", "totalTemplates": "Total Templates", "manage": "Manage Emails"}, "smsMarketing": {"title": "SMS Marketing", "description": "Send and manage SMS campaigns", "totalCampaigns": "Total Campaigns", "manage": "Manage SMS", "comingSoon": "SMS Marketing feature is under development. Please check back later!"}, "googleAds": {"title": "Google Ads", "description": "Integrate and manage Google Ads campaigns", "totalAccounts": "Accounts", "manage": "Manage Google Ads", "comingSoon": "This feature is under development. Please check back later!", "connectAccount": "Connect Account", "connectAccountDescription": "Account connection form will be developed in the next phase.", "name": "Account Name", "customerId": "Customer ID", "viewCampaigns": "View Campaigns", "accountCreated": "Google Ads account has been created successfully", "accountCreateError": "Error creating Google Ads account", "accountUpdated": "Google Ads account has been updated", "accountUpdateError": "Error updating Google Ads account", "accountDeleted": "Google Ads account has been deleted", "accountDeleteError": "Error deleting Google Ads account", "accounts": "Google Ads Accounts", "campaigns": "Google Ads Campaigns", "campaignName": "Campaign Name", "campaignId": "Campaign ID", "campaignType": "Campaign Type", "budget": "Budget", "startDate": "Start Date", "endDate": "End Date", "createCampaign": "Create New Campaign", "editCampaign": "Edit Campaign", "campaignCreated": "Google Ads campaign has been created successfully", "campaignCreateError": "Error creating Google Ads campaign", "campaignUpdated": "Google Ads campaign has been updated", "campaignUpdateError": "Error updating Google Ads campaign", "campaignDeleted": "Google Ads campaign has been deleted", "campaignDeleteError": "Error deleting Google Ads campaign", "campaignEnabled": "Google Ads campaign has been enabled", "campaignPaused": "Google Ads campaign has been paused", "campaignStatusUpdateError": "Error updating Google Ads campaign status", "campaignTypes": {"SEARCH": "Search", "DISPLAY": "Display", "VIDEO": "Video", "SHOPPING": "Shopping", "APP": "App", "PERFORMANCE_MAX": "Performance Max"}, "campaignStatuses": {"ENABLED": "Enabled", "PAUSED": "Paused", "REMOVED": "Removed", "DRAFT": "Draft"}, "tabs": {"accounts": "Accounts", "campaigns": "Campaigns", "performance": "Performance"}}, "facebookAds": {"title": "Facebook Ads", "description": "Integrate and manage Facebook Ads campaigns", "totalAccounts": "Accounts", "manage": "Manage Facebook Ads", "comingSoon": "This feature is under development. Please check back later!"}, "zalo": {"title": "Zalo OA/ZNS", "description": "Integrate Zalo Official Account, send ZNS messages", "totalAccounts": "Accounts", "manage": "<PERSON><PERSON>", "comingSoon": "This feature is under development. Please check back later!", "messages": "Messages", "automation": {"title": "Automation", "description": "Set up message automation"}, "analytics": {"title": "Analytics", "description": "Reports and performance analysis"}, "overview": {"title": "Zalo Marketing", "description": "Man<PERSON> Zalo Official Account and ZNS campaigns", "connectAccount": "Connect OA", "noAccounts": "No accounts yet", "noAccountsDescription": "Connect Zalo Official Account to get started", "connectFirstAccount": "Connect first account", "connectedAccounts": "Connected accounts", "connectedAccountsDescription": "Manage connected Zalo Official Accounts", "viewAllAccounts": "View all accounts", "stats": {"totalAccounts": "Total OAs", "activeAccounts": "Active", "totalFollowers": "Total Followers", "newFollowersToday": "+12 today", "messagesSent": "Messages sent", "messagesToday": "+89 today", "engagementRate": "Engagement rate", "increaseFromLastWeek": "+2.1% from last week"}}, "accounts": {"title": "Manage Zalo OA", "description": "Connect and manage Zalo Official Accounts", "connectNew": "Connect new OA", "searchPlaceholder": "Search by OA name...", "filters": {"title": "Filters", "advanced": "Advanced filters"}, "list": {"title": "Account list", "description": "Total {{count}} accounts", "noData": "No accounts yet"}, "table": {"name": "OA Name", "oaId": "OA ID", "followers": "Followers", "status": "Status", "lastUpdate": "Last update", "actions": "Actions"}, "connect": {"title": "Connect Zalo OA", "description": "Enter information to connect Zalo Official Account"}}, "followers": {"title": "Manage Followers", "description": "Followers of {{name}}", "descriptionDefault": "Manage followers list", "export": "Export", "sync": "Sync", "searchPlaceholder": "Search by name, phone...", "stats": {"totalFollowers": "Total Followers", "activeFollowers": "Active", "newThisWeek": "New this week", "selected": "Selected", "interacting": "Interacting", "increase": "Increase 15%"}, "actions": {"sendMessage": "Send message", "addTag": "Add tag"}, "bulkActions": {"title": "Bulk actions", "description": "Perform action for {{count}} selected followers", "addTag": "Add tag", "sendMessage": "Send message", "addTagVip": "Add \"VIP\" tag", "addTagNewCustomer": "Add \"New customer\" tag", "sendBulkMessage": "Send bulk message"}, "table": {"name": "Name", "phone": "Phone", "followDate": "Follow date", "lastInteraction": "Last interaction", "tags": "Tags", "status": "Status", "actions": "Actions"}}, "zns": {"title": "ZNS Templates", "description": "Manage Zalo Notification Service templates", "createTemplate": "Create Template", "searchPlaceholder": "Search templates...", "stats": {"totalTemplates": "Total Templates", "approved": "Approved", "pending": "Pending", "avgCost": "Avg Cost", "perMessage": "Per message", "newTemplates": "+2 new templates", "readyToUse": "Ready to use", "underReview": "Under review"}, "table": {"template": "Template", "templateId": "Template ID", "status": "Status", "quality": "Quality", "cost": "Cost", "params": "Parameters", "updated": "Updated", "actions": "Actions"}, "status": {"approved": "Approved", "pending": "Pending", "rejected": "Rejected", "disabled": "Disabled"}, "quality": {"high": "High", "normal": "Normal", "low": "Low"}, "preview": {"title": "Template Preview", "content": "Content:", "params": "Parameters:", "paramType": "STRING", "required": "Required"}, "create": {"title": "Create ZNS Template", "success": "Template created successfully", "description": "Create new ZNS notification template", "instructions": {"title": "ZNS Template Creation Guide", "description": "Template will be sent to Zalo for approval before use. Content must comply with Zalo's ZNS regulations. Use {param_name} to mark dynamic parameters."}, "form": {"oaLabel": "Select Official Account", "oaHelp": "Choose OA to create template", "oaPlaceholder": "Select Official Account...", "nameLabel": "Template Name", "nameHelp": "Descriptive name for template (internal use only)", "namePlaceholder": "Example: Order confirmation, Promotion notification...", "contentLabel": "Template Content", "contentHelp": "ZNS message content. Use {param_name} for dynamic parameters", "contentPlaceholder": "Example: Hello {customer_name}, your order #{order_id} has been confirmed with total value {total_amount} VND. Thank you for your purchase!"}}}}}}