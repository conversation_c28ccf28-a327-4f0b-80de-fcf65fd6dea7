import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, IsOptional, IsBoolean, IsNumber } from 'class-validator';

export class TextContentBlockDto {
  @ApiProperty({
    description: "The type of content block, fixed to 'text'.",
    enum: ['text'],
    example: 'text',
  })
  @IsEnum(['text'])
  @IsNotEmpty()
  type: 'text';

  @ApiProperty({
    description: 'The text content of the message.',
    example: 'Hello, how can I help you?',
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({
    description: 'Indicates if this text content has been edited after initial creation.',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  edited?: boolean;

  @ApiProperty({
    description: 'Timestamp when this text content was last edited (Unix timestamp in milliseconds). Only present in responses, not in requests.',
    example: 1749708424552,
    required: false,
    readOnly: true, // This field is only for responses
  })
  @IsOptional()
  @IsNumber()
  editedAt?: number;
}