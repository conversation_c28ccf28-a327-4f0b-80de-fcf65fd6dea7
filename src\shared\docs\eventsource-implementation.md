# EventSource Implementation with Custom Headers

Hướng dẫn implementation SSE với thư viện eventsource để hỗ trợ custom headers.

## ✅ Đã implement

1. **Thư viện eventsource**: Cài đặt `eventsource` và `@types/eventsource`
2. **Custom Headers**: Sử dụng custom fetch function để gửi Authorization header
3. **Backward Compatibility**: Giữ nguyên tất cả existing event handlers
4. **Error Handling**: Maintain existing error handling logic

## 🔧 Implementation Details

### 1. Package Installation
```bash
npm install eventsource @types/eventsource
```

### 2. Import Statement
```typescript
import { EventSource } from 'eventsource';
```

### 3. Custom Headers với Fetch Function
```typescript
this.eventSource = new EventSource(url, {
  fetch: (input, init) => fetch(input, {
    ...init,
    headers: {
      ...init?.headers,
      ...headers // Custom headers including Authorization
    },
    credentials: 'include'
  })
});
```

### 4. Authorization Header
```typescript
const headers: Record<string, string> = {
  'Accept': 'text/event-stream',
  'Cache-Control': 'no-cache'
};

// Thêm Authorization header nếu có token
if (this.getAuthToken) {
  const tokenResult = this.getAuthToken();
  const token = await Promise.resolve(tokenResult);
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
}
```

## 🎯 Key Features

### ✅ Custom Headers Support
- Authorization header với Bearer token
- Accept và Cache-Control headers
- Extensible cho thêm headers khác

### ✅ Async Token Support
- Hỗ trợ cả sync và async getAuthToken functions
- Promise.resolve() để handle cả hai cases

### ✅ Backward Compatibility
- Giữ nguyên tất cả existing event handlers
- Không thay đổi API interface
- Maintain existing error handling

### ✅ Debug Support
- Enhanced logging với token masking
- Connection status tracking
- Error details logging

## 🔍 Usage Example

```typescript
// Initialize SSE service với auth token
const sseService = new ChatSSEService(
  'https://api.example.com',
  true, // debug mode
  () => localStorage.getItem('authToken') || '' // getAuthToken
);

// Connect với custom headers
await sseService.connect(threadId, runId);
```

## 🚀 Expected Request

```http
GET /v1/chat/stream/events/{threadId}/{runId} HTTP/1.1
Host: api.example.com
Accept: text/event-stream
Cache-Control: no-cache
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 🔧 Debug Logs

```
[ChatSSEService] SSE URL: {url: "https://api.example.com/v1/chat/stream/events/..."}
[ChatSSEService] SSE Headers: {Accept: "text/event-stream", Authorization: "Bearer ***"}
[ChatSSEService] EventSource created with custom headers
[ChatSSEService] SSE connection opened successfully
```

## 🎯 Benefits

1. **Native EventSource API**: Sử dụng standard EventSource interface
2. **Custom Headers**: Hỗ trợ Authorization và custom headers
3. **Auto Reconnection**: Built-in reconnection logic của EventSource
4. **Cross-Platform**: Hoạt động trên browser và Node.js
5. **Type Safety**: Full TypeScript support

## 🔍 Comparison với Fetch Approach

| Feature | EventSource Library | Fetch + ReadableStream |
|---------|-------------------|------------------------|
| Custom Headers | ✅ Via custom fetch | ✅ Direct support |
| Auto Reconnection | ✅ Built-in | ❌ Manual implementation |
| Event Parsing | ✅ Automatic | ❌ Manual parsing |
| Browser Support | ✅ Native API | ✅ Modern browsers |
| Code Complexity | ✅ Simple | ❌ Complex |
| Memory Usage | ✅ Optimized | ❌ Higher |

## 🚨 Migration Notes

### From Native EventSource
- Thay đổi import statement
- Thêm custom fetch function
- Không thay đổi event handlers

### From Fetch Approach
- Xóa manual stream processing
- Xóa AbortController logic
- Giữ nguyên event handling logic

## 🔧 Troubleshooting

### Issue 1: Headers Not Sent
```typescript
// ✅ Correct
headers: {
  ...init?.headers,
  ...headers
}

// ❌ Wrong
headers: headers
```

### Issue 2: Token Not Available
```typescript
// ✅ Check token exists
if (token) {
  headers['Authorization'] = `Bearer ${token}`;
}
```

### Issue 3: CORS Issues
```typescript
// ✅ Include credentials
credentials: 'include'
```

Với implementation này, SSE connection sẽ có Authorization header và hoạt động như native EventSource với tất cả benefits của thư viện!
