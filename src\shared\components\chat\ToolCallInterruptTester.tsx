/**
 * ToolCallInterruptTester Component
 * Component để test tool call interrupt functionality
 */

import React from 'react';
import { ToolCallInterruptData } from '@/shared/services/chat-sse.service';

export interface ToolCallInterruptTesterProps {
  /**
   * Callback để trigger tool call interrupt
   */
  onTriggerInterrupt: (toolCallData: ToolCallInterruptData) => void;
}

/**
 * ToolCallInterruptTester Component
 */
const ToolCallInterruptTester: React.FC<ToolCallInterruptTesterProps> = ({
  onTriggerInterrupt
}) => {
  const handleTriggerTest = () => {
    const mockToolCallData: ToolCallInterruptData = {
      role: 'supervisor',
      toolName: 'web_search',
      toolDescription: 'Search the web for information',
      parameters: {
        query: 'latest AI news',
        limit: 10,
        language: 'vi'
      },
      threadId: 'test-thread-123',
      runId: 'test-run-456'
    };

    console.log('[ToolCallInterruptTester] Triggering test interrupt:', mockToolCallData);
    onTriggerInterrupt(mockToolCallData);
  };

  const handleTriggerComplexTest = () => {
    const mockToolCallData: ToolCallInterruptData = {
      role: 'assistant',
      toolName: 'file_manager',
      toolDescription: 'Manage files and directories on the system',
      parameters: {
        action: 'delete',
        path: '/important/documents/file.txt',
        recursive: true,
        force: true,
        backup: false
      },
      threadId: 'test-thread-789',
      runId: 'test-run-101'
    };

    console.log('[ToolCallInterruptTester] Triggering complex test interrupt:', mockToolCallData);
    onTriggerInterrupt(mockToolCallData);
  };

  const handleTriggerMinimalTest = () => {
    const mockToolCallData: ToolCallInterruptData = {
      role: 'worker',
      toolName: 'calculator',
      toolDescription: '',
      parameters: {},
      threadId: 'test-thread-minimal',
      runId: ''
    };

    console.log('[ToolCallInterruptTester] Triggering minimal test interrupt:', mockToolCallData);
    onTriggerInterrupt(mockToolCallData);
  };

  return (
    <div className="fixed top-4 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-4">
      <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
        🧪 Tool Call Interrupt Tester
      </h3>
      
      <div className="space-y-2">
        <button
          onClick={handleTriggerTest}
          className="w-full px-3 py-2 text-xs bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
        >
          🔍 Test Web Search
        </button>
        
        <button
          onClick={handleTriggerComplexTest}
          className="w-full px-3 py-2 text-xs bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
        >
          🗑️ Test File Delete (Complex)
        </button>
        
        <button
          onClick={handleTriggerMinimalTest}
          className="w-full px-3 py-2 text-xs bg-green-600 hover:bg-green-700 text-white rounded transition-colors"
        >
          🧮 Test Calculator (Minimal)
        </button>
      </div>
      
      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          Click buttons to simulate tool_call_interrupt events
        </p>
      </div>
    </div>
  );
};

export default ToolCallInterruptTester;
