/**
 * Types for audience custom field API
 */

import type { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Custom field data type enum - theo cấu trúc API mới
 */
export enum MarketingCustomFieldDataType {
  TEXT = 'text',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  SELECT = 'select',
  OBJECT = 'object',
}

/**
 * Legacy custom field type enum - để tương thích ngược
 */
export enum CustomFieldType {
  TEXT = 'string',
  NUMBER = 'number',
  DATE = 'date',
  BOOLEAN = 'boolean',
}

/**
 * Custom field status enum
 */
export enum CustomFieldStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Select option cho trường select - theo cấu trúc API mới
 */
export interface MarketingSelectOption {
  title: string;
  value: string;
}

/**
 * Config cho các loại trường tùy chỉnh - theo cấu trúc API mới
 */
export interface MarketingCustomFieldConfig {
  placeholder?: string;
  defaultValue?: string | number | boolean | Record<string, unknown>;
  // Text config
  pattern?: string;
  minLength?: number;
  maxLength?: number;
  // Number config
  minValue?: number;
  maxValue?: number;
  // Select config
  options?: MarketingSelectOption[];
}

/**
 * Request tạo trường tùy chỉnh mới - theo cấu trúc API mới
 */
export interface CreateMarketingCustomFieldRequest {
  fieldKey: string;
  displayName: string;
  dataType: MarketingCustomFieldDataType;
  description?: string;
  tags?: string[];
  config?: MarketingCustomFieldConfig;
}

/**
 * Request cập nhật trường tùy chỉnh - theo cấu trúc API mới
 */
export interface UpdateMarketingCustomFieldRequest {
  fieldKey?: string;
  displayName?: string;
  dataType?: MarketingCustomFieldDataType;
  description?: string;
  tags?: string[];
  config?: MarketingCustomFieldConfig;
}

/**
 * Response trường tùy chỉnh - theo cấu trúc API mới
 */
export interface MarketingCustomFieldResponse {
  id: number;
  fieldKey: string;
  userId: number;
  displayName: string;
  dataType: MarketingCustomFieldDataType;
  description?: string;
  tags: string[];
  config: MarketingCustomFieldConfig;
}

/**
 * Legacy custom field option
 */
export interface CustomFieldOption {
  id: string;
  label: string;
  value: string;
}

/**
 * Legacy custom field entity
 */
export interface CustomField {
  id: string;
  fieldKey: string;
  userId: string;
  displayName: string;
  dataType: string;
  description?: string;
  tags: string[];
  config: Record<string, unknown>;
  status?: CustomFieldStatus;
  isRequired?: boolean;
  options?: CustomFieldOption[];
  defaultValue?: string | number | boolean | string[];

  // Hỗ trợ tương thích ngược với API cũ
  name?: string;
  key?: string;
  type?: CustomFieldType;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Create custom field request
 */
export interface CreateCustomFieldRequest {
  fieldKey: string;
  displayName: string;
  dataType: CustomFieldType;
  description?: string;
  status?: CustomFieldStatus;
  isRequired?: boolean;
  options?: Omit<CustomFieldOption, 'id'>[];
  defaultValue?: string | number | boolean | string[];

  // Hỗ trợ tương thích ngược với API cũ
  name?: string;
  key?: string;
  type?: CustomFieldType;
}

/**
 * Update custom field request
 */
export interface UpdateCustomFieldRequest {
  fieldKey?: string;
  displayName?: string;
  dataType?: CustomFieldType;
  name?: string;
  description?: string;
  status?: CustomFieldStatus;
  isRequired?: boolean;
  options?: Omit<CustomFieldOption, 'id'>[];
  defaultValue?: string | number | boolean | string[];
}

/**
 * Custom field response
 */
export type CustomFieldResponse = CustomField;

/**
 * Custom field list response
 */
export type CustomFieldListResponse = ApiResponseDto<PaginatedResult<CustomFieldResponse>>;

/**
 * Custom field detail response
 */
export type CustomFieldDetailResponse = ApiResponseDto<CustomFieldResponse>;

/**
 * Custom field query params
 */
export interface CustomFieldQueryParams {
  search?: string | undefined;
  type?: CustomFieldType;
  status?: CustomFieldStatus;
  page?: number;
  limit?: number;
  sortBy?: string | undefined;
  sortDirection?: 'ASC' | 'DESC' | undefined;
}
