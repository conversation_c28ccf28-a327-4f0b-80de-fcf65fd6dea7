/**
 * <PERSON><PERSON> sách URL hình ảnh thật cho các chức năng <PERSON>alo
 */
export const functionImages = [
  // Zalo logo
  'https://cdn.haitrieu.com/wp-content/uploads/2022/01/Logo-Zalo-Arc.png',
  // Zalo function icons
  'https://cdn-icons-png.flaticon.com/512/2875/2875438.png', // Chat function
  'https://cdn-icons-png.flaticon.com/512/1968/1968666.png', // User identification
  'https://cdn-icons-png.flaticon.com/512/2665/2665038.png', // Document sharing
  'https://cdn-icons-png.flaticon.com/512/1828/1828640.png', // Phone verification
  'https://cdn-icons-png.flaticon.com/512/1055/1055183.png', // Analytics
  'https://cdn-icons-png.flaticon.com/512/2761/2761098.png', // AI assistant
  'https://cdn-icons-png.flaticon.com/512/1935/1935397.png', // Payment
  'https://cdn-icons-png.flaticon.com/512/1378/1378542.png', // Security
];

/**
 * Danh sách tên chức năng Zalo
 */
export const functionNames = [
  'GetProductInfo - Tư vấn sản phẩm chi tiết, chính xác để nâng cao trải nghiệm khách hàng',
  'IdentifyCustomer - Function định danh và nhận diện khách hàng',
  'SendAttachment - Function cung cấp tài liệu nhanh chóng, chính xác',
  'IsValidPhoneNumber - Function xác thực số điện thoại thông minh và chính xác',
  'AnalyticsData - Function phân tích dữ liệu khách hàng',
  'AIAssistant - Trợ lý ảo thông minh hỗ trợ khách hàng',
  'PaymentProcess - Xử lý thanh toán an toàn qua Zalo',
  'SecurityCheck - Kiểm tra bảo mật tài khoản người dùng',
];

/**
 * Lấy một URL hình ảnh ngẫu nhiên từ danh sách
 */
export const getRandomFunctionImage = (): string => {
  const randomIndex = Math.floor(Math.random() * functionImages.length);
  return functionImages[randomIndex] || '';
};

/**
 * Lấy một tên chức năng ngẫu nhiên từ danh sách
 */
export const getRandomFunctionName = (): string => {
  const randomIndex = Math.floor(Math.random() * functionNames.length);
  return functionNames[randomIndex] || '';
};

/**
 * Lấy một URL hình ảnh cụ thể dựa trên tên chức năng
 */
export const getFunctionImageByName = (name: string): string => {
  // Trích xuất phần đầu của tên chức năng (trước dấu gạch ngang)
  const functionCode = name.split(' - ')[0];

  switch (functionCode) {
    case 'GetProductInfo':
      return functionImages[1] || ''; // Chat function
    case 'IdentifyCustomer':
      return functionImages[2] || ''; // User identification
    case 'SendAttachment':
      return functionImages[3] || ''; // Document sharing
    case 'IsValidPhoneNumber':
      return functionImages[4] || ''; // Phone verification
    case 'AnalyticsData':
      return functionImages[5] || ''; // Analytics
    case 'AIAssistant':
      return functionImages[6] || ''; // AI assistant
    case 'PaymentProcess':
      return functionImages[7] || ''; // Payment
    case 'SecurityCheck':
      return functionImages[8] || ''; // Security
    default:
      return functionImages[0] || ''; // Zalo logo
  }
};
